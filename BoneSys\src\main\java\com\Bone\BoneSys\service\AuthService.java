package com.Bone.BoneSys.service;

import com.Bone.BoneSys.entity.User;
import com.Bone.BoneSys.repository.UserRepository;
import com.Bone.BoneSys.util.PasswordUtil;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.util.Date;
import java.util.Optional;

@Service
public class AuthService {
    private final UserRepository userRepository;

    @Value("${jwt.secret:BoneSysSecretKeyForJWTTokenGeneration2025}")
    private String jwtSecret;

    @Value("${jwt.expiration:86400000}")
    private long jwtExpiration;

    public AuthService(UserRepository userRepository) {
        this.userRepository = userRepository;
    }

    /**
     * 登录校验：首次登录使用厂家密码，后续使用用户密码
     * @param password 用户输入的密码
     * @return LoginResult（包含token和匹配类型）
     * @throws Exception 登录失败抛出异常
     */
    public LoginResult login(String password) throws Exception {
        Optional<User> userOpt = userRepository.findTheOnlyUser();
        if (userOpt.isEmpty()) {
            throw new Exception("用户不存在");
        }
        User user = userOpt.get();
        
        String storedUserPassword = user.getUserPasswordHash();
        String storedFactoryPassword = user.getFactoryPasswordHash();
        
        // 检查是否为首次登录（用户密码为空或为默认值）
        boolean isFirstLogin = (storedUserPassword == null || 
                               storedUserPassword.isEmpty() || 
                               storedUserPassword.equals("default") ||
                               storedUserPassword.equals(storedFactoryPassword));
        
        if (isFirstLogin) {
            // 首次登录：只能使用厂家密码
            if (!password.equals(storedFactoryPassword)) {
                throw new Exception("首次登录请使用厂家密码");
            }
            
            // 生成JWT
            String token = Jwts.builder()
                    .setSubject(user.getUsername())
                    .setIssuedAt(new Date())
                    .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
                    .signWith(SignatureAlgorithm.HS256, jwtSecret)
                    .compact();
            
            user.setLastUpdatedAt(java.time.LocalDateTime.now());
            userRepository.save(user);
            return new LoginResult(token, "factory");
        } else {
            // 非首次登录：只能使用用户密码
            if (!password.equals(storedUserPassword)) {
                throw new Exception("请使用用户密码登录");
            }
            
            // 生成JWT
            String token = Jwts.builder()
                    .setSubject(user.getUsername())
                    .setIssuedAt(new Date())
                    .setExpiration(new Date(System.currentTimeMillis() + jwtExpiration))
                    .signWith(SignatureAlgorithm.HS256, jwtSecret)
                    .compact();
            
            user.setLastUpdatedAt(java.time.LocalDateTime.now());
            userRepository.save(user);
            return new LoginResult(token, "user");
        }
    }

    /**
     * 密码重置：厂家密码校验通过后，设置新用户密码
     */
    public void resetUserPassword(String factoryPassword, String newUserPassword) throws Exception {
        Optional<User> userOpt = userRepository.findTheOnlyUser();
        if (userOpt.isEmpty()) {
            throw new Exception("用户不存在");
        }
        User user = userOpt.get();
        
        // 验证厂家密码
        String storedFactoryPassword = user.getFactoryPasswordHash();
        if (storedFactoryPassword == null || !factoryPassword.equals(storedFactoryPassword)) {
            throw new Exception("厂家密码错误");
        }
        
        // 验证新密码强度
        if (!PasswordUtil.isValidPassword(newUserPassword)) {
            throw new Exception("新密码不符合要求，至少6位字符");
        }
        
        // 设置新的用户密码
        user.setUserPasswordHash(newUserPassword);
        user.setLastUpdatedAt(java.time.LocalDateTime.now());
        userRepository.save(user);
    }

    /**
     * 修改用户密码：用户密码校验通过后，设置新用户密码（简化版）
     */
    public void changeUserPassword(String oldUserPassword, String newUserPassword) throws Exception {
        Optional<User> userOpt = userRepository.findTheOnlyUser();
        if (userOpt.isEmpty()) {
            throw new Exception("用户不存在");
        }
        User user = userOpt.get();
        
        // 简化密码验证：直接比较明文密码
        String storedPassword = user.getUserPasswordHash();
        if (storedPassword == null || storedPassword.isEmpty()) {
            throw new Exception("当前密码未设置");
        }
        
        if (!oldUserPassword.equals(storedPassword)) {
            throw new Exception("原用户密码错误");
        }
        
        // 直接存储明文密码
        user.setUserPasswordHash(newUserPassword);
        user.setLastUpdatedAt(java.time.LocalDateTime.now());
        userRepository.save(user);
    }

    /**
     * 检查用户状态：是否为首次登录
     */
    public boolean isFirstLogin() throws Exception {
        Optional<User> userOpt = userRepository.findTheOnlyUser();
        if (userOpt.isEmpty()) {
            throw new Exception("用户不存在");
        }
        User user = userOpt.get();
        
        String storedUserPassword = user.getUserPasswordHash();
        String storedFactoryPassword = user.getFactoryPasswordHash();
        
        return (storedUserPassword == null || 
                storedUserPassword.isEmpty() || 
                storedUserPassword.equals("default") ||
                storedUserPassword.equals(storedFactoryPassword));
    }

    public static class LoginResult {
        private final String token;
        private final String matchedType; // "factory" 或 "user"
        public LoginResult(String token, String matchedType) {
            this.token = token;
            this.matchedType = matchedType;
        }
        public String getToken() { return token; }
        public String getMatchedType() { return matchedType; }
    }
}