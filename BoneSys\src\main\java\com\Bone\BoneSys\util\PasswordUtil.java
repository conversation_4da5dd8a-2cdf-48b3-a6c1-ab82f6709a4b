package com.Bone.BoneSys.util;

/**
 * 密码工具类（简化版）
 * 用于离线医疗设备的简单密码管理
 */
public class PasswordUtil {
    
    /**
     * 默认用户密码
     */
    public static final String DEFAULT_USER_PASSWORD = "admin123";
    
    /**
     * 默认厂家密码
     */
    public static final String DEFAULT_FACTORY_PASSWORD = "factory123";
    
    /**
     * 验证密码强度（简化版）
     * @param password 密码
     * @return 是否符合要求
     */
    public static boolean isValidPassword(String password) {
        if (password == null || password.trim().isEmpty()) {
            return false;
        }
        // 简单要求：长度至少6位
        return password.length() >= 6;
    }
    
    /**
     * 验证密码是否为默认密码
     * @param password 密码
     * @return 是否为默认密码
     */
    public static boolean isDefaultPassword(String password) {
        return DEFAULT_USER_PASSWORD.equals(password) || DEFAULT_FACTORY_PASSWORD.equals(password);
    }
    
    /**
     * 获取密码提示信息
     * @return 密码提示
     */
    public static String getPasswordHint() {
        return "默认用户密码: " + DEFAULT_USER_PASSWORD + ", 厂家密码: " + DEFAULT_FACTORY_PASSWORD;
    }
}