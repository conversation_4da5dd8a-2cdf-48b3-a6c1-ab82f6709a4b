import org.springframework.security.crypto.bcrypt.BCrypt;

public class PasswordDecoder {
    public static void main(String[] args) {
        String hash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou";
        
        // 常见的密码候选
        String[] candidates = {
            "admin",
            "admin123", 
            "123456",
            "password",
            "freebone",
            "FREEBONE",
            "bone123",
            "factory123",
            "user123",
            "hello.world333",
            "Mybone",
            "bonesys",
            "BoneSys",
            "factory",
            "user",
            "test123",
            "admin888",
            "bone888"
        };
        
        System.out.println("Testing BCrypt hash: " + hash);
        System.out.println("==========================================");
        
        for (String candidate : candidates) {
            if (BCrypt.checkpw(candidate, hash)) {
                System.out.println("✅ FOUND MATCH: '" + candidate + "'");
                return;
            } else {
                System.out.println("❌ No match: " + candidate);
            }
        }
        
        System.out.println("==========================================");
        System.out.println("No matches found in candidate list.");
    }
}