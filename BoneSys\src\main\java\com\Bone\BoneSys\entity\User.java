package com.Bone.BoneSys.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.AllArgsConstructor;

import java.time.LocalDateTime;

/**
 * 用户实体类
 * 对应数据库表：users
 */
@Entity
@Table(name = "users")
@Data
@NoArgsConstructor
@AllArgsConstructor
public class User {
    
    /**
     * 用户ID
     */
    @Id
    @Column(name = "id")
    private Integer id;
    
    /**
     * 用户名
     */
    @Column(name = "username", unique = true, nullable = false, length = 50)
    private String username;
    
    /**
     * 厂家密码哈希值
     */
    @Column(name = "factory_password_hash", nullable = false)
    private String factoryPasswordHash;
    
    /**
     * 用户自定义密码哈希值
     */
    @Column(name = "user_password_hash", nullable = false)
    private String userPasswordHash;
    
    /**
     * 最后更新时间
     */
    @Column(name = "last_updated_at")
    private LocalDateTime lastUpdatedAt;
    
    /**
     * 更新时间戳
     */
    @PreUpdate
    @PrePersist
    public void updateTimestamp() {
        this.lastUpdatedAt = LocalDateTime.now();
    }
}