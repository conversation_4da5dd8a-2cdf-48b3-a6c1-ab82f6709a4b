-- ====================================================================================
-- FREEBONE医疗系统 - 持久化数据库设置脚本
-- 解决数据库重启后数据消失的问题
-- ====================================================================================

-- 1. 创建数据库（如果不存在）
CREATE DATABASE IF NOT EXISTS `bonesys` 
DEFAULT CHARACTER SET utf8mb4 
DEFAULT COLLATE utf8mb4_unicode_ci;

USE `bonesys`;

-- 2. 设置数据库持久化参数
SET GLOBAL innodb_flush_log_at_trx_commit = 1;
SET GLOBAL sync_binlog = 1;
SET GLOBAL innodb_doublewrite = 1;

-- 3. 创建用户表（如果不存在）
CREATE TABLE IF NOT EXISTS `users` (
  `id` INT NOT NULL,
  `username` VARCHAR(50) NOT NULL COMMENT '用户名',
  `factory_password_hash` VARCHAR(255) NOT NULL COMMENT '厂家密码',
  `user_password_hash` VARCHAR(255) NOT NULL COMMENT '用户密码',
  `last_updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `username_UNIQUE` (`username`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='用户表';

-- 4. 插入默认用户（首次登录状态）
INSERT INTO `users` (`id`, `username`, `factory_password_hash`, `user_password_hash`) VALUES
(1, 'admin', 'factory123', 'factory123')  -- 用户密码初始等于厂家密码，表示首次登录
ON DUPLICATE KEY UPDATE 
    factory_password_hash = VALUES(factory_password_hash);

-- 5. 创建其他必要的表（如果不存在）
CREATE TABLE IF NOT EXISTS `patients` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `patient_card_id` VARCHAR(50) NOT NULL COMMENT '就诊卡号',
  `name` VARCHAR(100) NOT NULL COMMENT '患者姓名',
  `gender` VARCHAR(10) DEFAULT NULL COMMENT '性别',
  `age` VARCHAR(10) DEFAULT NULL COMMENT '年龄',
  `contact_info` VARCHAR(200) DEFAULT NULL COMMENT '联系方式',
  `created_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `patient_card_id_UNIQUE` (`patient_card_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='患者表';

CREATE TABLE IF NOT EXISTS `records` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_number` VARCHAR(50) NOT NULL COMMENT '档案编号',
  `patient_id` BIGINT NOT NULL COMMENT '患者ID',
  `diagnosis_description` TEXT COMMENT '诊断描述',
  `sessions_completed_count` INT DEFAULT 0 COMMENT '已完成会话数',
  `created_at` DATE NOT NULL COMMENT '创建日期',
  PRIMARY KEY (`id`),
  UNIQUE KEY `record_number_UNIQUE` (`record_number`),
  KEY `fk_records_patient_id` (`patient_id`),
  CONSTRAINT `fk_records_patient_id` FOREIGN KEY (`patient_id`) REFERENCES `patients` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='档案表';

CREATE TABLE IF NOT EXISTS `treatment_heads` (
  `head_number` INT NOT NULL COMMENT '治疗头编号',
  `status` ENUM('AVAILABLE','IN_USE','MAINTENANCE') NOT NULL DEFAULT 'AVAILABLE' COMMENT '状态',
  `last_updated_at` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '最后更新时间',
  PRIMARY KEY (`head_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='治疗头表';

CREATE TABLE IF NOT EXISTS `processes` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_id` BIGINT NOT NULL COMMENT '档案ID',
  `treatment_mode` ENUM('ON_SITE','TAKE_AWAY') NOT NULL COMMENT '治疗模式',
  `status` ENUM('IN_PROGRESS','COMPLETED','CANCELLED') NOT NULL DEFAULT 'IN_PROGRESS' COMMENT '状态',
  `start_time` TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '开始时间',
  `end_time` TIMESTAMP NULL DEFAULT NULL COMMENT '结束时间',
  PRIMARY KEY (`id`),
  KEY `fk_processes_record_id` (`record_id`),
  CONSTRAINT `fk_processes_record_id` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='治疗进程表';

CREATE TABLE IF NOT EXISTS `treatment_details` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `process_id` BIGINT NOT NULL COMMENT '进程ID',
  `head_number_used` INT NOT NULL COMMENT '使用的治疗头编号',
  `body_part` VARCHAR(50) NOT NULL COMMENT '治疗部位',
  `duration` INT NOT NULL COMMENT '治疗时长(分钟)',
  `intensity` DECIMAL(10,2) NOT NULL COMMENT '治疗强度',
  `frequency` INT NOT NULL COMMENT '治疗频率',
  `patch_type` ENUM('SHALLOW','DEEP') NOT NULL COMMENT '贴片类型',
  `patch_quantity` INT NOT NULL COMMENT '贴片数量',
  `status` ENUM('TREATING','COMPLETED','AWAITING_RETURN','RETURNED') NOT NULL DEFAULT 'TREATING' COMMENT '状态',
  PRIMARY KEY (`id`),
  KEY `fk_treatment_details_process_id` (`process_id`),
  KEY `fk_treatment_details_head_number` (`head_number_used`),
  CONSTRAINT `fk_treatment_details_process_id` FOREIGN KEY (`process_id`) REFERENCES `processes` (`id`) ON DELETE CASCADE,
  CONSTRAINT `fk_treatment_details_head_number` FOREIGN KEY (`head_number_used`) REFERENCES `treatment_heads` (`head_number`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='治疗详情表';

CREATE TABLE IF NOT EXISTS `body_part_stats` (
  `id` BIGINT NOT NULL AUTO_INCREMENT,
  `record_id` BIGINT NOT NULL COMMENT '档案ID',
  `body_part` VARCHAR(50) NOT NULL COMMENT '治疗部位',
  `total_usage_count` INT NOT NULL DEFAULT 0 COMMENT '总使用次数',
  `total_duration_minutes` INT NOT NULL DEFAULT 0 COMMENT '总治疗时长(分钟)',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_record_body_part` (`record_id`, `body_part`),
  KEY `fk_body_part_stats_record_id` (`record_id`),
  CONSTRAINT `fk_body_part_stats_record_id` FOREIGN KEY (`record_id`) REFERENCES `records` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='部位统计表';

-- 6. 插入默认治疗头数据
INSERT IGNORE INTO `treatment_heads` (`head_number`, `status`) VALUES
(1, 'AVAILABLE'), (2, 'AVAILABLE'), (3, 'AVAILABLE'), (4, 'AVAILABLE'), (5, 'AVAILABLE'),
(6, 'AVAILABLE'), (7, 'AVAILABLE'), (8, 'AVAILABLE'), (9, 'AVAILABLE'), (10, 'AVAILABLE'),
(11, 'AVAILABLE'), (12, 'AVAILABLE'), (13, 'AVAILABLE'), (14, 'AVAILABLE'), (15, 'AVAILABLE'),
(16, 'AVAILABLE'), (17, 'AVAILABLE'), (18, 'AVAILABLE'), (19, 'AVAILABLE'), (20, 'AVAILABLE');

-- 7. 显示初始化结果
SELECT '数据库持久化设置完成' as '状态';
SELECT 
    id as '用户ID',
    username as '用户名',
    factory_password_hash as '厂家密码',
    CASE 
        WHEN user_password_hash = factory_password_hash THEN '首次登录状态'
        ELSE user_password_hash
    END as '用户密码状态',
    last_updated_at as '更新时间'
FROM users WHERE id = 1;

-- 8. 显示使用说明
SELECT '首次登录请使用厂家密码: factory123' as '登录说明';
SELECT '登录后请通过密码重置设置用户密码' as '设置说明';