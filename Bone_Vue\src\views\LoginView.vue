<!-- src/views/LoginView.vue -->
<script setup lang="ts">
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';

// 静态导入图片资源
import logoImage from '@/assets/images/login/94ba9fc357ac1731d8758d94f0b98d05.png';
import passwordIcon from '@/assets/images/login/aaa28ee80252446131ad309e4ea8bc2a.png';
import inputLine from '@/assets/images/login/418bad088dc9845f99672504680f4fd6.png';
import eyeIcon from '@/assets/images/login/ffed879f51e05f73d5f48d3fc90c28e0.png';
import loginIcon from '@/assets/images/login/07727ce0ec230bc9e6d8ed1247ad7418.png';
import bottomLogo from '@/assets/images/login/1d9c10ed0305d3151e41acb4998f2f1c.png';
import bgImage from '@/assets/images/login/6ce9f9861ced27679296af9c9ac6c482.png';
import boxBg from '@/assets/images/login/********************************.png';
import loginBtnBg from '@/assets/images/login/8b5f27b4d61eee989b13cfe693bdf49d.png';
import inputBg from '@/assets/images/login/680c683b8cfaea5137d473c73bd994eb.png';

const router = useRouter();
const password = ref('');
const pressTimer = ref<number | null>(null);
const isPressed = ref(false);

// 登录功能
const onSubmit = () => {
  if (password.value === '123456') {
    // 设置登录状态
    localStorage.setItem('isAuthenticated', 'true');
    MessagePlugin.success('登录成功');
    // 延迟跳转确保消息显示
    setTimeout(() => {
      router.push('/home');
    }, 1000);
  } else {
    MessagePlugin.error('密码错误');
  }
};

// 长按功能实现 - 最终优化版本
const startPress = (event: Event) => {
  // 防止事件冒泡和默认行为
  event.preventDefault();
  event.stopPropagation();
  
  console.log('开始长按');
  isPressed.value = true;
  
  // 清除之前的定时器（如果有的话）
  if (pressTimer.value) {
    clearTimeout(pressTimer.value);
  }
  
  pressTimer.value = setTimeout(() => {
    if (isPressed.value) {
      console.log('长按触发');
      // 长按3秒后跳转到设置页面
      MessagePlugin.success('长按触发跳转到设置页面');
      
      // 先清除定时器引用
      pressTimer.value = null;
      isPressed.value = false; // 重置状态
      
      // 使用 nextTick 确保定时器执行完毕后再跳转
      setTimeout(() => {
        // 跳转到设置页面
        router.push('/settings')
          .then(() => {
            console.log('路由跳转成功');
          })
          .catch(err => {
            console.error('路由跳转失败:', err);
            MessagePlugin.error('跳转失败');
          });
      }, 100);
    }
  }, 3000); // 3秒长按
};

const endPress = (event: Event) => {
  // 防止事件冒泡和默认行为
  event.preventDefault();
  event.stopPropagation();
  
  console.log('结束长按');
  isPressed.value = false;
  
  if (pressTimer.value) {
    clearTimeout(pressTimer.value);
    pressTimer.value = null;
  }
};

// 调试函数可以删除
const debugPress = () => {
  console.log('调试：长按开始');
  startPress(new Event('debug'));
};

const debugRelease = () => {
  console.log('调试：长按结束');
  endPress(new Event('debug'));
};
</script>

<template>
  <div class="page flex-col">
    <div class="section_1 flex-col">
      <div class="section_2 flex-row">
        <div class="box_1 flex-col">
          <img
            class="image_1"
            referrerpolicy="no-referrer"
            :src="logoImage"
          />
          <div class="text-group_1 flex-col justify-between">
            <div class="text-wrapper_1">
              <span class="text_1">Hello</span>
              <span class="text_2">，</span>
            </div>
            <span class="text_3">Welcome.</span>
          </div>
          <div class="section_3 flex-row justify-between">
            <div class="block_1 flex-row">
              <img
                class="label_1"
                referrerpolicy="no-referrer"
                :src="passwordIcon"
              />
              <div class="box_2 flex-col justify-between">
                <t-input
                  v-model="password"
                  type="password"
                  size="large"
                  placeholder="请输入密码"
                  letter-spacing="4px"
                  @keydown.enter="onSubmit"
                  class="password-input"
                />
                <img
                  class="image_2"
                  referrerpolicy="no-referrer"
                  :src="inputLine"
                />
              </div>
            </div>
            <div class="block_2 flex-col" @click="onSubmit">
              <div class="group_1 flex-row">
                <div class="image-wrapper_1 flex-col">
                  <img
                    class="image_3"
                    referrerpolicy="no-referrer"
                    :src="loginIcon"
                  />
                </div>
                <span class="text_5">登录</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      <!-- 进一步优化事件绑定 -->
      <div 
        class="image-wrapper_2 flex-row"
        @touchstart.prevent="startPress"
        @touchend.prevent="endPress"
        @touchcancel.prevent="endPress"
        @mousedown.prevent="startPress"
        @mouseup.prevent="endPress"
        @mouseleave.prevent="endPress"
        @click.prevent 
      >
        <img
          class="image_4"
          referrerpolicy="no-referrer"
          :src="bottomLogo"
        />
      </div>
    </div>
  </div>
</template>

<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.section_1 {
  height: 1080px;
  background: url('@/assets/images/login/6ce9f9861ced27679296af9c9ac6c482.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.section_2 {
  width: 1319px;
  height: 779px;
  margin: 116px 0 0 301px;
}

.box_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  width: 1319px;
  height: 779px;
  border: 1px solid rgba(96, 96, 96, 1);
}

.image_1 {
  width: 677px;
  height: 109px;
  margin: 91px 0 0 135px;
}

.text-group_1 {
  width: 254px;
  height: 101px;
  margin: 61px 0 0 144px;
}

.text-wrapper_1 {
  width: 157px;
  height: 43px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: ArialMT;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 31px;
  margin-left: 3px;
}

.text_1 {
  width: 157px;
  height: 43px;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 50px;
  font-family: ArialMT;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 31px;
}

.text_2 {
  width: 157px;
  height: 43px;
  overflow-wrap: break-word;
  color: rgba(248, 128, 36, 1);
  font-size: 50px;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 31px;
}

.text_3 {
  width: 254px;
  height: 38px;
  overflow-wrap: break-word;
  color: rgba(59, 59, 59, 1);
  font-size: 50px;
  font-family: ArialMT;
  font-weight: normal;
  text-align: left;
  white-space: nowrap;
  line-height: 29px;
  margin-top: 20px;
}

.section_3 {
  width: 617px;
  height: 69px;
  margin: 189px 0 159px 350px;
}

.block_1 {
  width: 333px;
  height: 56px;
  background: url('@/assets/images/login/********************************.png') -9px 0px no-repeat;
  background-size: 352px 78px;
  margin-top: 7px;
}

.label_1 {
  width: 32px;
  height: 36px;
  margin: 9px 0 0 33px;
}

.box_2 {
  width: 177px;
  height: 2px;
  margin: 16px 0 0 24px;
  position: relative;
}

.password-input {
  width: 190px;
  height: 20px;
  border: none !important;
  background: transparent !important;
  outline: none !important;
  font-size: 20px;
  letter-spacing: 4px;
  font-family: MicrosoftYaHei;
  margin-top: -10px;
  box-shadow: none !important;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
  padding: 0 !important;          /* 移除内边距 */
  margin: -12px !important;           /* 移除外边距 */
}

/* 深度选择器，覆盖组件内部样式 */
:deep(.t-input) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

:deep(.t-input__inner) {
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}
.custom-placeholder {
  position: absolute;
  top: 0;
  left: 0;
  width: 190px;
  height: 20px;
  color: rgba(116, 116, 116, 1);
  font-size: 20px;
  font-family: MicrosoftYaHei;
  letter-spacing: 4px;
  text-align: center;
  line-height: 20px;
  z-index: 1;
  letter-spacing: 4px;
  pointer-events: none; /* 让点击事件穿透到输入框 */
}
.image_2 {
  position: absolute;
  width: 190px;
  height: 1px;
  margin-top: 28px;
}

.label_2 {
  width: 24px;
  height: 21px;
  margin: 17px 23px 0 20px;
}

.block_2 {
  height: 69px;
  background: url('@/assets/images/login/8b5f27b4d61eee989b13cfe693bdf49d.png') -9px 0px no-repeat;
  background-size: 252px 92px;
  width: 232px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.block_2:hover {
  transform: scale(1.05);
}

.block_2:active {
  transform: scale(0.98);
}

.group_1 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  width: 226px;
  height: 64px;
  margin: 3px 0 0 3px;
}

.image-wrapper_1 {
  height: 37px;
  background: url('@/assets/images/login/680c683b8cfaea5137d473c73bd994eb.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 35px;
  margin: 13px 0 0 43px;
}

.image_3 {
  width: 37px;
  height: 20px;
  margin: 8px 0 0 -7px;
}

.text_5 {
  width: 67px;
  height: 26px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 27px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin: 18px 50px 0 31px;
}

.image-wrapper_2 {
  width: 138px;
  height: 50px;
  margin: 91px 0 44px 1708px;
  cursor: pointer;
  user-select: none;
  -webkit-user-select: none;
}

.image_4 {
  width: 138px;
  height: 50px;
  transition: transform 0.2s ease;
}

.image-wrapper_2:active .image_4 {
  transform: scale(0.95);
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style> 