import { createRouter, createWebHistory } from 'vue-router'
import HomeView from '../views/HomeView.vue'
import LoginView from '../views/LoginView.vue'
import PatientManagementView from '../views/PatientManagementView.vue'
import NewPatientView from '../views/NewPatientView.vue'

const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      redirect: '/login' // 将根路径重定向到登录页
    },
    {
      path: '/home',
      name: 'home',
      component: HomeView,
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/login',
      name: 'login',
      component: LoginView
    },
    {
      path: '/patients',
      name: 'patients',
      component: PatientManagementView,
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/patient/:id',
      name: 'patientDetail',
      component: () => import('../views/PatientDetailView.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/new-patient',
      name: 'newPatient',
      component: NewPatientView,
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/treatment/new/:patientId',
      name: 'newTreatment',
      component: () => import('../views/TreatmentSettingsView.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/treatment/process/:patientId',
      name: 'treatmentProcess',
      component: () => import('../views/TreatmentProcessView.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/treatment/process1/:patientId',
      name: 'treatmentProcess1',
      component: () => import('../views/TreatmentProcessView1.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/treatment/process2/:patientId',
      name: 'treatmentProcess2',
      component: () => import('../views/TreatmentProcessView2.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/settings',
      name: 'settings',
      component: () => import('../views/SettingView.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/process-management',
      name: 'ProcessManagement',
      meta: {
        requiresAuth: true
      },
      component: () => import('../views/ProcessMangementView.vue'),
    },
    {
      path: '/treatment-head-management',
      name: 'treatmentHeadManagement',
      component: () => import('../views/TreatmentHeadManagementView.vue'),
      meta: {
        requiresAuth: true
      }
    },
    {
      path: '/about',
      name: 'about',
      // route level code-splitting
      // this generates a separate chunk (About.[hash].js) for this route
      // which is lazy-loaded when the route is visited.
      component: () => import('../views/AboutView.vue')
    }
  ]
})

// 路由守卫，检查登录状态
router.beforeEach((to, from, next) => {
  // 临时模拟登录状态，实际项目中应该检查token或session
  const isAuthenticated = localStorage.getItem('isAuthenticated') === 'true'
  
  if (to.meta.requiresAuth && !isAuthenticated) {
    // 如果需要认证但未登录，重定向到登录页
    next({ name: 'login' })
  } else if (to.path === '/login' && isAuthenticated) {
    // 如果已登录但访问登录页，重定向到主页
    next({ name: 'home' })
  } else {
    next()
  }
})

// 清除之前保存的登录状态，确保每次启动都需要重新登录
// 仅在开发阶段使用，实际部署时可以根据需要移除
localStorage.removeItem('isAuthenticated')

export default router
