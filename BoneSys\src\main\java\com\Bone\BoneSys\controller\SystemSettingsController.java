package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.*;

import java.util.Arrays;
import java.util.List;

/**
 * 系统设置控制器
 * 对应UI页面：新版-设置页面.png
 */
@RestController
@RequestMapping("/api/settings")
@CrossOrigin(origins = "*")
public class SystemSettingsController {

    private static final Logger logger = LoggerFactory.getLogger(SystemSettingsController.class);

    // 默认系统设置值
    private int volume = 75;
    private String screenTimeout = "20min";
    private String language = "中文";
    private List<String> reminderTimes = Arrays.asList("10min", "15min", "20min");

    /**
     * 获取系统设置
     * GET /api/settings/system
     */
    @GetMapping("/system")
    public ApiResponse<SystemSettingsResponse> getSystemSettings() {
        try {
            logger.info("Fetching system settings");

            SystemSettingsResponse response = new SystemSettingsResponse();
            response.setVolume(volume);
            response.setScreenTimeout(screenTimeout);
            response.setLanguage(language);
            response.setReminderTimes(reminderTimes);

            return ApiResponse.success("系统设置获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching system settings", e);
            return ApiResponse.error(500, "获取系统设置失败: " + e.getMessage());
        }
    }

    /**
     * 更新系统设置
     * PUT /api/settings/system
     */
    @PutMapping("/system")
    public ApiResponse<Void> updateSystemSettings(@RequestBody UpdateSystemSettingsRequest request) {
        try {
            logger.info("Updating system settings: {}", request);

            // 验证音量范围
            if (request.getVolume() != null) {
                if (request.getVolume() < 0 || request.getVolume() > 100) {
                    return ApiResponse.error(400, "音量必须在0-100之间");
                }
                this.volume = request.getVolume();
            }

            // 验证屏幕超时时间
            if (request.getScreenTimeout() != null) {
                List<String> validTimeouts = Arrays.asList("10min", "20min", "30min", "60min", "never");
                if (!validTimeouts.contains(request.getScreenTimeout())) {
                    return ApiResponse.error(400, "无效的屏幕超时时间");
                }
                this.screenTimeout = request.getScreenTimeout();
            }

            // 验证语言设置
            if (request.getLanguage() != null) {
                List<String> validLanguages = Arrays.asList("中文", "English");
                if (!validLanguages.contains(request.getLanguage())) {
                    return ApiResponse.error(400, "不支持的语言");
                }
                this.language = request.getLanguage();
            }

            // 验证提醒时间
            if (request.getReminderTimes() != null) {
                List<String> validReminderTimes = Arrays.asList("5min", "10min", "15min", "20min", "30min");
                for (String time : request.getReminderTimes()) {
                    if (!validReminderTimes.contains(time)) {
                        return ApiResponse.error(400, "无效的提醒时间: " + time);
                    }
                }
                this.reminderTimes = request.getReminderTimes();
            }

            logger.info("System settings updated successfully");
            return ApiResponse.success("系统设置更新成功");

        } catch (Exception e) {
            logger.error("Error updating system settings", e);
            return ApiResponse.error(500, "更新系统设置失败: " + e.getMessage());
        }
    }

    /**
     * 重置系统设置为默认值
     * POST /api/settings/system/reset
     */
    @PostMapping("/system/reset")
    public ApiResponse<Void> resetSystemSettings() {
        try {
            logger.info("Resetting system settings to defaults");

            this.volume = 75;
            this.screenTimeout = "20min";
            this.language = "中文";
            this.reminderTimes = Arrays.asList("10min", "15min", "20min");

            return ApiResponse.success("系统设置已重置为默认值");

        } catch (Exception e) {
            logger.error("Error resetting system settings", e);
            return ApiResponse.error(500, "重置系统设置失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统设置选项
     * GET /api/settings/system/options
     */
    @GetMapping("/system/options")
    public ApiResponse<SystemSettingsOptionsResponse> getSystemSettingsOptions() {
        try {
            logger.info("Fetching system settings options");

            SystemSettingsOptionsResponse response = new SystemSettingsOptionsResponse();
            response.setVolumeRange(new VolumeRange(0, 100));
            response.setScreenTimeoutOptions(Arrays.asList("10min", "20min", "30min", "60min", "never"));
            response.setLanguageOptions(Arrays.asList("中文", "English"));
            response.setReminderTimeOptions(Arrays.asList("5min", "10min", "15min", "20min", "30min"));

            return ApiResponse.success("系统设置选项获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching system settings options", e);
            return ApiResponse.error(500, "获取系统设置选项失败: " + e.getMessage());
        }
    }

    // DTO类定义
    @Data
    public static class SystemSettingsResponse {
        private Integer volume;
        private String screenTimeout;
        private String language;
        private List<String> reminderTimes;
    }

    @Data
    public static class UpdateSystemSettingsRequest {
        private Integer volume;
        private String screenTimeout;
        private String language;
        private List<String> reminderTimes;
    }

    @Data
    public static class SystemSettingsOptionsResponse {
        private VolumeRange volumeRange;
        private List<String> screenTimeoutOptions;
        private List<String> languageOptions;
        private List<String> reminderTimeOptions;
    }

    @Data
    public static class VolumeRange {
        private int min;
        private int max;

        public VolumeRange(int min, int max) {
            this.min = min;
            this.max = max;
        }
    }
}
