# 🔐 FREEBONE医疗系统 - 简化认证指南

## 📋 概述

本系统已简化为明文密码认证，适用于离线医疗设备环境。不再使用复杂的BCrypt加密，提供更直接的密码管理方式。

## 🔑 密码说明

### 厂家密码
- **密码**: `factory123`
- **用途**: 
  - 首次登录使用
  - 密码重置时的验证密码

### 用户密码
- **初始状态**: 等于厂家密码（表示首次登录）
- **设置后**: 用户自定义密码
- **用途**: 日常系统登录使用（设置后）

## 🚀 登录方式

### 1. 检查登录状态
```bash
curl -X GET "http://localhost:8080/api/status"
```

### 2. 首次登录（使用厂家密码）
```bash
curl -X POST "http://localhost:8080/api/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "factory123"}'
```

### 3. 日常登录（使用用户密码）
```bash
curl -X POST "http://localhost:8080/api/login" \
  -H "Content-Type: application/json" \
  -d '{"password": "用户设置的密码"}'
```

### 成功响应
```json
{
  "success": true,
  "message": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzI1NiJ9...",
    "matchedType": "user"
  }
}
```

## 🔧 密码管理

### 1. 修改用户密码
```bash
curl -X POST "http://localhost:8080/api/change-password" \
  -H "Content-Type: application/json" \
  -d '{
    "oldPassword": "admin123",
    "newPassword": "新密码"
  }'
```

### 2. 重置用户密码（使用厂家密码）
```bash
curl -X POST "http://localhost:8080/api/reset-password" \
  -H "Content-Type: application/json" \
  -d '{
    "factoryPassword": "factory123",
    "newPassword": "新密码"
  }'
```

## 📊 数据库结构

### users表
```sql
CREATE TABLE `users` (
  `id` INT NOT NULL,
  `username` VARCHAR(50) NOT NULL,
  `factory_password_hash` VARCHAR(255) NOT NULL,  -- 现在存储明文厂家密码
  `user_password_hash` VARCHAR(255) NOT NULL,     -- 现在存储明文用户密码
  `last_updated_at` TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
  PRIMARY KEY (`id`)
);
```

### 默认数据
```sql
INSERT INTO `users` VALUES 
(1, 'admin', 'factory123', 'admin123', NOW());
```

## 🔄 从BCrypt迁移

如果你的系统之前使用BCrypt加密，运行以下SQL脚本进行迁移：

```sql
-- 执行密码迁移脚本
source SQL/update_passwords_to_plaintext.sql;
```

## ⚠️ 安全注意事项

1. **离线环境**: 此简化认证适用于离线医疗设备
2. **网络隔离**: 确保设备不连接外部网络
3. **物理安全**: 依赖物理访问控制保护设备
4. **定期更改**: 建议定期更改默认密码

## 🧪 测试验证

运行测试验证认证系统：

```bash
# 编译项目
./gradlew compileJava

# 运行认证测试
./gradlew test --tests SimplifiedAuthTest
```

## 📝 代码示例

### Java中的密码验证
```java
import com.Bone.BoneSys.util.PasswordUtil;

// 检查是否为默认密码
boolean isDefault = PasswordUtil.isDefaultPassword("admin123");

// 验证密码强度
boolean isValid = PasswordUtil.isValidPassword("newpassword");

// 获取密码提示
String hint = PasswordUtil.getPasswordHint();
```

## 🔍 故障排除

### 常见问题

1. **登录失败**: 确认使用密码 `admin123`
2. **密码重置失败**: 确认厂家密码 `factory123`
3. **数据库连接问题**: 检查数据库配置和连接

### 日志查看
```bash
# 查看应用日志
tail -f logs/application.log

# 查看认证相关日志
grep "AuthService" logs/application.log
```

## 📞 支持

如有问题，请检查：
1. 数据库连接是否正常
2. 密码是否正确输入
3. 系统日志中的错误信息

---

**更新时间**: 2025年1月28日  
**版本**: 简化认证版本 v1.0