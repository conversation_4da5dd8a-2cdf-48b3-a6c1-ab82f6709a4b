/**
 * 在线BCrypt测试工具
 * 由于无法直接运行BCrypt库，我们使用在线工具的逻辑来分析
 */
public class OnlineBCryptTest {
    public static void main(String[] args) {
        String hash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou";
        
        System.out.println("🔐 BCrypt密码验证分析");
        System.out.println("哈希值: " + hash);
        System.out.println();
        
        // 分析哈希结构
        System.out.println("📊 哈希结构分析:");
        System.out.println("- 算法: $2a$ (BCrypt)");
        System.out.println("- 成本因子: 10");
        System.out.println("- Salt: N.zmdr9k7uOCQb376NoUnu");
        System.out.println("- 哈希: TJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou");
        System.out.println();
        
        // 候选密码列表（按可能性排序）
        String[] candidates = {
            "admin123",      // 最高可能性 - 多个文档证据
            "hello.world333", // 中等可能性 - 配置文件中出现
            "admin",         // 中等可能性 - 简单用户名
            "123456",        // 低可能性 - 常见密码
            "password",      // 低可能性 - 常见密码
            "freebone",      // 低可能性 - 项目相关
            "Mybone",        // 低可能性 - 数据库密码
            "bonesys",       // 低可能性 - 项目名
            "factory123"     // 低可能性 - 厂家密码
        };
        
        System.out.println("🎯 推荐测试顺序:");
        for (int i = 0; i < candidates.length; i++) {
            String priority = i < 2 ? "🔥 高" : i < 4 ? "⚡ 中" : "💡 低";
            System.out.println((i + 1) + ". " + candidates[i] + " - " + priority + "优先级");
        }
        
        System.out.println();
        System.out.println("📝 测试建议:");
        System.out.println("1. 首先尝试 'admin123' - 有最多文档证据支持");
        System.out.println("2. 如果不行，尝试 'hello.world333' - 在配置文件中出现");
        System.out.println("3. 然后按顺序测试其他候选密码");
        
        System.out.println();
        System.out.println("🔧 验证方法:");
        System.out.println("- 使用登录接口: POST /api/login");
        System.out.println("- 请求体: {\"password\": \"候选密码\"}");
        System.out.println("- 成功返回: JWT token");
        System.out.println("- 失败返回: 401错误");
    }
}