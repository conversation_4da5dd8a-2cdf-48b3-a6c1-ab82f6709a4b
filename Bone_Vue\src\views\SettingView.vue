<!-- src/views/SettingView.vue -->
<template>
  <div class="page flex-col">
    <div class="box_1 flex-col">
      <div class="group_1 flex-row">
        <div class="block_1 flex-col">
          <div 
            class="image-wrapper_1 flex-row" 
            @click="goToLogin"
          >
            <img
              class="image_1"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/1d55adcbeba78c03a661e95bdec4b8b8.png"
            />
          </div>
          <div class="text-wrapper_1 flex-row"><span class="text_1">设置</span></div>
          <div class="group_2 flex-row">
            <span class="text_2">密码设置:</span>
            <div class="text-wrapper_2 flex-col"><span class="text_3">密码重置</span></div>
            <div class="text-wrapper_3 flex-col"><span class="text_4">修改密码</span></div>
          </div>
          <div class="group_3 flex-row">
            <span class="text_5">音量设置:</span>
            <img
              class="image_2"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/b94dac48fdda827b2088f6841c5c98bf.png"
            />
            <div class="volume-slider-container">
              <input 
                type="range" 
                min="0" 
                max="100" 
                v-model="volume" 
                class="volume-slider"
                @input="onVolumeChange"
              />
            </div>
            <img
              class="image_4"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/d803a03d9949ca46fa239e0a7421032a.png"
            />
          </div>
          <div class="group_4 flex-row">
            <span class="text_6">息屏设置:</span>
            <div class="text-wrapper_4 flex-col"><span class="text_7">20min</span></div>
            <div class="image-wrapper_2 flex-col">
              <img
                class="label_1"
                referrerpolicy="no-referrer"
                src="../assets/images/settings/f38e81b4006851ede9a94ff15868e062.png"
              />
            </div>
          </div>
          <div class="group_5 flex-row">
            <span class="text_8">语言设置:</span>
            <div class="text-wrapper_5 flex-col"><span class="text_9">中文</span></div>
            <div class="text-wrapper_6 flex-col"><span class="text_10">ENGLISH</span></div>
          </div>
          <div class="group_6 flex-row">
            <span class="text_11">提醒设置:</span>
            <div class="text-wrapper_7 flex-col"><span class="text_12">10min</span></div>
            <div class="text-wrapper_8 flex-col"><span class="text_13">15min</span></div>
            <div class="text-wrapper_9 flex-col"><span class="text_14">20min</span></div>
          </div>
          <div class="image-wrapper_3 flex-row">
            <img
              class="image_5"
              referrerpolicy="no-referrer"
              src="../assets/images/settings/87946cae3cd6b695bd0468693af11b26.png"
            />
          </div>
       
        </div>
      </div>
      <div class="image-wrapper_4 flex-row">
        <img
          class="image_6"
          referrerpolicy="no-referrer"
          src="../assets/images/settings/7a65d20cdb94191fc58c995ea8c418a2.png"
        />
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';

const router = useRouter();

// 音量控制
const volume = ref(50); // 默认音量50%

// 音量变化处理
const onVolumeChange = () => {
  console.log('当前音量:', volume.value);
  // 这里可以添加实际的音量控制逻辑
  // 例如调用系统音量API或保存到本地存储
  localStorage.setItem('volume', volume.value.toString());
};

// 跳转到登录页面
const goToLogin = () => {
  // 清除认证状态
  localStorage.removeItem('isAuthenticated');
  // 跳转到登录页面
  router.push('/login');
};

// 组件挂载时读取保存的音量设置
onMounted(() => {
  const savedVolume = localStorage.getItem('volume');
  if (savedVolume) {
    volume.value = parseInt(savedVolume, 10);
  }
});
</script>

<style scoped>
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.box_1 {
  height: 1080px;
  background: url('../assets/images/settings/79feb2122d04465638aba851729e875a.png') 100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.group_1 {
  width: 1319px;
  height: 779px;
  margin: 116px 0 0 301px;
}

.block_1 {
  box-shadow: 16px 28px 33px 25px rgba(28, 27, 26, 0.06);
  background-color: rgba(255, 255, 255, 1);
  border-radius: 5px;
  height: 779px;
  border: 1px solid rgba(96, 96, 96, 1);
  width: 1319px;
  position: relative;
}

.image-wrapper_1 {
  width: 60px;
  height: 60px;
  margin: 31px 0 0 1216px;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.image-wrapper_1:hover {
  transform: scale(1.1);
}

.image_1 {
  width: 60px;
  height: 60px;
}

.text-wrapper_1 {
  width: 131px;
  height: 48px;
  margin-left: 608px;
}

.text_1 {
  width: 131px;
  height: 48px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.group_2 {
  width: 716px;
  height: 61px;
  margin: 90px 0 0 221px;
}

.text_2 {
  width: 209px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 9px;
}

.text-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 196px;
  margin: -2px 0 0 72px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-wrapper_2:hover {
  background-color: rgba(200, 200, 200, 1);
}

.text_3 {
  width: 153px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 20px;
}

.text-wrapper_3 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 196px;
  margin: -2px -2px 0 45px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.text-wrapper_3:hover {
  background-color: rgba(200, 200, 200, 1);
}

.text_4 {
  width: 152px;
  height: 34px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 20px;
}

.group_3 {
  width: 689px;
  height: 46px;
  margin: 41px 0 0 222px;
  display: flex;
  align-items: center;
}

.text_5 {
  width: 208px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.image_2 {
  width: 50px;
  height: 45px;
  margin-left: 116px;
}

.volume-slider-container {
  width: 220px;
  height: 3px;
  margin: 0 26px;
  position: relative;
  background: #ddd;
}

.volume-slider {
  -webkit-appearance: none;
  width: 100%;
  height: 100%;
  background: transparent;
  outline: none;
  position: absolute;
  top: 0;
  left: 0;
  margin: 0;
  padding: 0;
}

.volume-slider::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 40px;
  border-radius: 6px; /* 改为圆角矩形 */
  background: #EF8029;
  cursor: pointer;
  border: 2px solid #878482;
  position: relative;
  z-index: 2;
}

.volume-slider::-moz-range-thumb {
  width: 20px;
  height: 40px;
  border-radius: 6px; /* 改为圆角矩形 */
  background: #EF8029;
  cursor: pointer;
  border: 2px solid #878482;
}


.image_4 {
  width: 50px;
  height: 45px;
  margin: 1px 0 0 19px;
}

.group_4 {
  width: 618px;
  height: 61px;
  margin: 45px 0 0 221px;
}

.text_6 {
  width: 209px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
}

.text-wrapper_4 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 72px;
}

.text_7 {
  width: 123px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 26px;
}

.image-wrapper_2 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 15px;
  height: 41px;
  width: 97px;
  margin: 11px 0 0 67px;
}

.label_1 {
  width: 33px;
  height: 33px;
  margin: 5px 0 0 53px;
}

.group_5 {
  width: 672px;
  height: 61px;
  margin: 32px 0 0 222px;
}

.text_8 {
  width: 208px;
  height: 42px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 0px;
}

.text-wrapper_5 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 72px;
}

.text_9 {
  width: 71px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(89, 89, 89, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 53px;
}

.text-wrapper_6 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 48px;
}

.text_10 {
  width: 127px;
  height: 19px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 23px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 29px;
  margin: 20px 0 0 25px;
}

.group_6 {
  width: 896px;
  height: 61px;
  margin: 34px 0 0 222px;
}

.text_11 {
  width: 208px;
  height: 41px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 41px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 3px;
}

.text-wrapper_7 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 72px;
}

.text_12 {
  width: 121px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 28px;
}

.text-wrapper_8 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px 0 0 46px;
}

.text_13 {
  width: 121px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(110, 110, 110, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 28px;
}

.text-wrapper_9 {
  box-shadow: 3px 4px 26px 0px rgba(29, 35, 36, 0.08);
  background-color: rgba(226, 226, 226, 1);
  border-radius: 16px;
  height: 65px;
  border: 2px solid rgba(153, 153, 153, 1);
  width: 173px;
  margin: -2px -2px 0 53px;
}

.text_14 {
  width: 123px;
  height: 27px;
  overflow-wrap: break-word;
  color: rgba(255, 255, 255, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 26px;
}

.image-wrapper_3 {
  width: 160px;
  height: 25px;
  margin: 33px 0 50px 599px;
}

.image_5 {
  width: 160px;
  height: 25px;
}



.image-wrapper_4 {
  width: 138px;
  height: 50px;
  margin: 91px 0 44px 1682px;
}

.image_6 {
  width: 138px;
  height: 50px;
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}
</style> 