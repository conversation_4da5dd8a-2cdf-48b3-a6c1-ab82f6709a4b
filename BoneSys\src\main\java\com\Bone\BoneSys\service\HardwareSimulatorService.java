package com.Bone.BoneSys.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.stereotype.Service;

import java.util.Random;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 硬件模拟器服务
 * 在没有真实硬件的环境中模拟硬件响应
 * 通过配置 hardware.simulator.enabled=true 启用
 */
@Service
@ConditionalOnProperty(name = "hardware.simulator.enabled", havingValue = "true")
public class HardwareSimulatorService {
    private static final Logger logger = LoggerFactory.getLogger(HardwareSimulatorService.class);
    
    // 模拟治疗头状态
    private final ConcurrentHashMap<Integer, SimulatedTreatmentHead> simulatedHeads = new ConcurrentHashMap<>();
    private final Random random = new Random();
    
    public HardwareSimulatorService() {
        initializeSimulatedHeads();
        logger.info("Hardware simulator initialized with 20 simulated treatment heads");
    }
    
    /**
     * 初始化模拟的治疗头
     */
    private void initializeSimulatedHeads() {
        for (int i = 1; i <= 20; i++) {
            SimulatedTreatmentHead head = new SimulatedTreatmentHead();
            head.headNumber = i;
            head.slotNumber = i;
            // 确保有足够的治疗头可用于测试：70-100%电量
            head.batteryLevel = 70 + random.nextInt(31); // 70-100%
            head.usageCount = random.nextInt(100); // 0-99次
            head.lightColor = 0; // 默认关闭
            head.isWorking = false;
            simulatedHeads.put(i, head);
        }
    }
    
    /**
     * 模拟TRZI指令响应 - 查询所有治疗头数据
     */
    public String simulateTRZIResponse() {
        StringBuilder response = new StringBuilder("TRZI20"); // 20个治疗头
        for (int i = 1; i <= 20; i++) {
            SimulatedTreatmentHead head = simulatedHeads.get(i);
            response.append(String.format("%02d%02d%03d%02d", 
                head.headNumber, 
                head.batteryLevel, 
                head.usageCount, 
                head.slotNumber));
        }
        response.append("\r\n");
        logger.debug("Simulated TRZI response: {}", response.toString());
        return response.toString();
    }
    
    /**
     * 模拟TWSC指令响应 - 点亮指示灯
     */
    public String simulateTWSCResponse(String command) {
        try {
            // 解析命令：TWSC + 数量(2) + (头号(2) + 颜色(1)) * 数量
            String data = command.substring(4).replace("\\r\\n", "");
            int headCount = Integer.parseInt(data.substring(0, 2));
            StringBuilder response = new StringBuilder("TWSC");
            response.append(String.format("%02d", headCount));
            
            String headsData = data.substring(2);
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 3;
                int headNumber = Integer.parseInt(headsData.substring(startIndex, startIndex + 2));
                int colorCode = Integer.parseInt(headsData.substring(startIndex + 2, startIndex + 3));
                
                // 更新模拟状态
                SimulatedTreatmentHead head = simulatedHeads.get(headNumber);
                if (head != null) {
                    head.lightColor = colorCode;
                }
                
                // 构建响应：头号(2) + 颜色(1) + 槽号(2)
                response.append(String.format("%02d%d%02d", headNumber, colorCode, headNumber));
            }
            response.append("\r\n");
            logger.debug("Simulated TWSC response: {}", response.toString());
            return response.toString();
        } catch (Exception e) {
            logger.error("Error simulating TWSC response", e);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 模拟TWSN指令响应 - 关闭指示灯
     */
    public String simulateTWSNResponse(String command) {
        try {
            String data = command.substring(4).replace("\\r\\n", "");
            int headCount = Integer.parseInt(data.substring(0, 2));
            StringBuilder response = new StringBuilder("TWSN");
            response.append(String.format("%02d", headCount));
            
            String headsData = data.substring(2);
            for (int i = 0; i < headCount; i++) {
                int startIndex = i * 2;
                int headNumber = Integer.parseInt(headsData.substring(startIndex, startIndex + 2));
                
                // 更新模拟状态
                SimulatedTreatmentHead head = simulatedHeads.get(headNumber);
                if (head != null) {
                    head.lightColor = 0; // 关闭
                }
                
                response.append(String.format("%02d", headNumber));
            }
            response.append("\r\n");
            logger.debug("Simulated TWSN response: {}", response.toString());
            return response.toString();
        } catch (Exception e) {
            logger.error("Error simulating TWSN response", e);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 模拟TWSDT指令响应 - 发送治疗参数
     */
    public String simulateTWSDTResponse(String command) {
        // TWSDT指令返回相同的指令格式表示成功
        logger.debug("Simulated TWSDT response: {}", command);
        return command;
    }
    
    /**
     * 模拟TWS指令响应 - 开始治疗
     */
    public String simulateTWSResponse(String command) {
        try {
            // 解析治疗头编号和参数
            String data = command.substring(3).replace("\r\n", "");
            int headNumber = Integer.parseInt(data.substring(0, 2));
            
            // 更新模拟状态
            SimulatedTreatmentHead head = simulatedHeads.get(headNumber);
            if (head != null) {
                head.isWorking = true;
            }
            
            // TWS指令返回相同的格式表示成功
            logger.debug("Simulated TWS response: {}", command);
            return command;
        } catch (Exception e) {
            logger.error("Error simulating TWS response", e);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 模拟TWZO指令响应 - 停止治疗
     */
    public String simulateTWZOResponse(String command) {
        try {
            // 解析治疗头编号
            String data = command.substring(4).replace("\\r\\n", "");
            int headNumber = Integer.parseInt(data);
            
            // 更新模拟状态
            SimulatedTreatmentHead head = simulatedHeads.get(headNumber);
            if (head != null) {
                head.isWorking = false;
                head.usageCount++; // 增加使用次数
            }
            
            logger.debug("Simulated TWZO response: {}", command);
            return command;
        } catch (Exception e) {
            logger.error("Error simulating TWZO response", e);
            return "ERROR\\r\\n";
        }
    }
    
    /**
     * 根据指令类型生成模拟响应
     */
    public String generateSimulatedResponse(String command) {
        // 添加随机延迟模拟真实硬件响应时间
        try {
            Thread.sleep(ThreadLocalRandom.current().nextInt(50, 200));
        } catch (InterruptedException e) {
            Thread.currentThread().interrupt();
        }
        
        if (command.startsWith("TRZI")) {
            return simulateTRZIResponse();
        } else if (command.startsWith("TWSC")) {
            return simulateTWSCResponse(command);
        } else if (command.startsWith("TWSN")) {
            return simulateTWSNResponse(command);
        } else if (command.startsWith("TWSDT")) {
            return simulateTWSDTResponse(command);
        } else if (command.startsWith("TWS")) {
            return simulateTWSResponse(command);
        } else if (command.startsWith("TWZO")) {
            return simulateTWZOResponse(command);
        } else {
            logger.warn("Unknown command for simulation: {}", command);
            return "ERROR\r\n";
        }
    }
    
    /**
     * 获取模拟治疗头状态
     */
    public SimulatedTreatmentHead getSimulatedHead(int headNumber) {
        return simulatedHeads.get(headNumber);
    }
    
    /**
     * 模拟治疗头数据结构
     */
    public static class SimulatedTreatmentHead {
        public int headNumber;
        public int slotNumber;
        public int batteryLevel;
        public int usageCount;
        public int lightColor;
        public boolean isWorking;
        
        @Override
        public String toString() {
            return String.format("SimulatedHead{num=%d, slot=%d, battery=%d%%, usage=%d, light=%d, working=%s}", 
                               headNumber, slotNumber, batteryLevel, usageCount, lightColor, isWorking);
        }
    }
}