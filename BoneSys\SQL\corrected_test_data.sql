-- ====================================================================================
-- FREEBONE医疗系统 - 修正版测试数据脚本
-- 治疗部位限定为：肩颈部、腰背部、髋部、上肢、下肢、其他部位
-- ====================================================================================

USE `bonesys`;

-- 1. 插入用户数据（简化版：使用明文密码）
INSERT INTO `users` (`id`, `username`, `factory_password_hash`, `user_password_hash`) VALUES
(1, 'admin', 'factory123', 'admin123')
ON DUPLICATE KEY UPDATE 
    username = VALUES(username),
    factory_password_hash = VALUES(factory_password_hash),
    user_password_hash = VALUES(user_password_hash);

-- 2. 插入患者数据（20个患者，符合新建档案界面显示）
INSERT INTO `patients` (`patient_card_id`, `name`, `gender`, `age`, `contact_info`, `created_at`) VALUES
('P20250101001', '张三', '男', '45', '13800138001', '2025-01-01 09:00:00'),
('P20250102002', '李四', '女', '38', '13800138002', '2025-01-02 10:30:00'),
('P20250103003', '王五', '男', '52', '13800138003', '2025-01-03 14:15:00'),
('P20250104004', '赵六', '女', '29', '13800138004', '2025-01-04 16:45:00'),
('P20250105005', '钱七', '男', '61', '13800138005', '2025-01-05 08:20:00'),
('P20250106006', '孙八', '女', '34', '13800138006', '2025-01-06 11:10:00'),
('P20250107007', '周九', '男', '47', '13800138007', '2025-01-07 13:30:00'),
('P20250108008', '吴十', '女', '26', '13800138008', '2025-01-08 15:50:00'),
('P20250109009', '郑十一', '男', '55', '13800138009', '2025-01-09 09:40:00'),
('P20250110010', '王十二', '女', '42', '13800138010', '2025-01-10 12:20:00'),
('P20250111011', '李十三', '男', '33', '13800138011', '2025-01-11 14:00:00'),
('P20250112012', '张十四', '女', '48', '13800138012', '2025-01-12 16:30:00'),
('P20250113013', '刘十五', '男', '39', '13800138013', '2025-01-13 10:15:00'),
('P20250114014', '陈十六', '女', '31', '13800138014', '2025-01-14 11:45:00'),
('P20250115015', '杨十七', '男', '56', '13800138015', '2025-01-15 13:25:00'),
('P20250116016', '黄十八', '女', '27', '13800138016', '2025-01-16 15:10:00'),
('P20250117017', '赵十九', '男', '44', '13800138017', '2025-01-17 08:50:00'),
('P20250118018', '周二十', '女', '37', '13800138018', '2025-01-18 10:35:00'),
('P20250119019', '吴二一', '男', '50', '13800138019', '2025-01-19 12:15:00'),
('P20250120020', '郑二二', '女', '35', '13800138020', '2025-01-20 14:40:00')
ON DUPLICATE KEY UPDATE name = VALUES(name);

-- 3. 插入档案数据
INSERT INTO `records` (`record_number`, `patient_id`, `diagnosis_description`, `sessions_completed_count`, `created_at`) VALUES
('R20250101001', 1, '颈椎病，C5-C6椎间盘突出', 8, '2025-01-01'),
('R20250102001', 2, '腰椎间盘突出，L4-L5', 12, '2025-01-02'),
('R20250103001', 3, '肩周炎，右肩关节活动受限', 6, '2025-01-03'),
('R20250104001', 4, '膝关节炎，双膝疼痛', 15, '2025-01-04'),
('R20250105001', 5, '腰肌劳损，慢性腰痛', 10, '2025-01-05'),
('R20250106001', 6, '颈肩综合征', 7, '2025-01-06'),
('R20250107001', 7, '腰椎滑脱，L5椎体前滑', 9, '2025-01-07'),
('R20250108001', 8, '网球肘，右肘外侧疼痛', 5, '2025-01-08'),
('R20250109001', 9, '坐骨神经痛', 11, '2025-01-09'),
('R20250110001', 10, '胸椎小关节紊乱', 4, '2025-01-10'),
('R20250111001', 11, '足底筋膜炎', 6, '2025-01-11'),
('R20250112001', 12, '肩袖损伤，左肩', 8, '2025-01-12'),
('R20250113001', 13, '腕管综合征', 3, '2025-01-13'),
('R20250114001', 14, '髌骨软化症', 7, '2025-01-14'),
('R20250115001', 15, '颈椎生理曲度变直', 9, '2025-01-15'),
('R20250116001', 16, '腰椎管狭窄', 13, '2025-01-16'),
('R20250117001', 17, '肩胛骨内侧缘疼痛', 5, '2025-01-17'),
('R20250118001', 18, '踝关节扭伤后遗症', 4, '2025-01-18'),
('R20250119001', 19, '胸椎侧弯', 6, '2025-01-19'),
('R20250120001', 20, '梨状肌综合征', 8, '2025-01-20')
ON DUPLICATE KEY UPDATE diagnosis_description = VALUES(diagnosis_description);

-- 4. 插入治疗进程数据（不同状态的进程）
INSERT INTO `processes` (`record_id`, `treatment_mode`, `status`, `start_time`, `end_time`) VALUES
-- 已完成的进程
(1, 'ON_SITE', 'COMPLETED', '2025-01-01 09:30:00', '2025-01-01 10:30:00'),
(2, 'TAKE_AWAY', 'COMPLETED', '2025-01-02 11:00:00', '2025-01-02 11:20:00'),
(3, 'ON_SITE', 'COMPLETED', '2025-01-03 15:30:00', '2025-01-03 16:30:00'),
(4, 'ON_SITE', 'COMPLETED', '2025-01-04 17:00:00', '2025-01-04 18:00:00'),
(5, 'TAKE_AWAY', 'COMPLETED', '2025-01-05 09:00:00', '2025-01-05 09:20:00'),
(6, 'ON_SITE', 'COMPLETED', '2025-01-06 10:00:00', '2025-01-06 11:00:00'),
(7, 'TAKE_AWAY', 'COMPLETED', '2025-01-07 14:00:00', '2025-01-07 14:20:00'),
(8, 'ON_SITE', 'COMPLETED', '2025-01-08 16:00:00', '2025-01-08 17:00:00'),
(9, 'ON_SITE', 'COMPLETED', '2025-01-09 11:00:00', '2025-01-09 12:00:00'),
(10, 'TAKE_AWAY', 'COMPLETED', '2025-01-10 13:30:00', '2025-01-10 13:50:00'),

-- 正在进行的进程
(11, 'ON_SITE', 'IN_PROGRESS', '2025-01-26 09:00:00', NULL),
(12, 'ON_SITE', 'IN_PROGRESS', '2025-01-26 10:30:00', NULL),
(13, 'TAKE_AWAY', 'IN_PROGRESS', '2025-01-26 11:00:00', NULL),

-- 已取消的进程
(14, 'ON_SITE', 'CANCELLED', '2025-01-15 10:00:00', '2025-01-15 10:15:00'),

-- 更多已完成的进程
(15, 'ON_SITE', 'COMPLETED', '2025-01-16 15:00:00', '2025-01-16 16:00:00'),
(16, 'TAKE_AWAY', 'COMPLETED', '2025-01-17 09:00:00', '2025-01-17 09:20:00'),
(17, 'ON_SITE', 'COMPLETED', '2025-01-18 11:00:00', '2025-01-18 12:00:00'),
(18, 'ON_SITE', 'COMPLETED', '2025-01-19 14:00:00', '2025-01-19 15:00:00'),
(19, 'TAKE_AWAY', 'COMPLETED', '2025-01-20 16:00:00', '2025-01-20 16:20:00');

-- 5. 插入治疗详情数据（使用正确的治疗部位）
INSERT INTO `treatment_details` (`process_id`, `head_number_used`, `body_part`, `duration`, `intensity`, `frequency`, `patch_type`, `patch_quantity`, `status`) VALUES
-- 已完成的治疗详情（使用6个标准部位）
(1, 3, '肩颈部', 20, 500.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(1, 5, '肩颈部', 15, 450.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(2, 12, '腰背部', 25, 600.00, 1000, 'DEEP', 3, 'RETURNED'),
(3, 2, '肩颈部', 20, 480.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(3, 4, '上肢', 15, 420.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(4, 6, '下肢', 30, 550.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(5, 14, '腰背部', 20, 520.00, 1000, 'DEEP', 2, 'RETURNED'),
(6, 7, '肩颈部', 18, 480.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(7, 15, '腰背部', 22, 580.00, 1000, 'DEEP', 3, 'RETURNED'),
(8, 1, '上肢', 15, 400.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(9, 11, '腰背部', 25, 560.00, 1000, 'DEEP', 2, 'COMPLETED'),
(10, 8, '其他部位', 20, 460.00, 1000, 'SHALLOW', 2, 'RETURNED'),

-- 正在进行的治疗详情
(11, 4, '肩颈部', 20, 500.00, 1000, 'SHALLOW', 2, 'TREATING'),
(12, 10, '肩颈部', 25, 480.00, 1000, 'SHALLOW', 1, 'TREATING'),
(13, 16, '腰背部', 30, 600.00, 1000, 'DEEP', 3, 'AWAITING_RETURN'),

-- 更多已完成的治疗详情
(15, 9, '下肢', 20, 480.00, 1000, 'SHALLOW', 2, 'COMPLETED'),
(16, 13, '肩颈部', 18, 500.00, 1000, 'DEEP', 2, 'RETURNED'),
(17, 17, '下肢', 15, 380.00, 1000, 'SHALLOW', 1, 'COMPLETED'),
(18, 18, '其他部位', 22, 500.00, 1000, 'DEEP', 2, 'COMPLETED'),
(19, 19, '髋部', 28, 620.00, 1000, 'DEEP', 4, 'RETURNED'),

-- 多部位治疗示例
(11, 20, '腰背部', 15, 450.00, 1000, 'DEEP', 1, 'COMPLETED'),
(12, 12, '上肢', 10, 350.00, 1000, 'SHALLOW', 1, 'COMPLETED');

-- 6. 插入部位统计数据（基于6个标准部位）
INSERT INTO `body_part_stats` (`record_id`, `body_part`, `total_usage_count`, `total_duration_minutes`) VALUES
-- 基于实际治疗详情计算的统计
(1, '肩颈部', 2, 35),
(2, '腰背部', 1, 25),
(3, '肩颈部', 1, 20),
(3, '上肢', 1, 15),
(4, '下肢', 1, 30),
(5, '腰背部', 1, 20),
(6, '肩颈部', 1, 18),
(7, '腰背部', 1, 22),
(8, '上肢', 1, 15),
(9, '腰背部', 1, 25),
(10, '其他部位', 1, 20),
(11, '肩颈部', 1, 20),
(11, '腰背部', 1, 15),
(12, '肩颈部', 1, 25),
(12, '上肢', 1, 10),
(13, '腰背部', 1, 30),
(15, '下肢', 1, 20),
(16, '肩颈部', 1, 18),
(17, '下肢', 1, 15),
(18, '其他部位', 1, 22),
(20, '髋部', 1, 28);

-- 验证数据插入结果
SELECT '数据插入完成，以下是统计信息：' as message;

-- 显示数据统计
SELECT 'users' as table_name, COUNT(*) as count FROM users
UNION ALL
SELECT 'patients', COUNT(*) FROM patients
UNION ALL
SELECT 'records', COUNT(*) FROM records
UNION ALL
SELECT 'treatment_heads', COUNT(*) FROM treatment_heads
UNION ALL
SELECT 'processes', COUNT(*) FROM processes
UNION ALL
SELECT 'treatment_details', COUNT(*) FROM treatment_details
UNION ALL
SELECT 'body_part_stats', COUNT(*) FROM body_part_stats;

-- 显示治疗部位分布（验证只使用6个标准部位）
SELECT '=== 治疗部位分布验证 ===' as info;
SELECT 
    body_part as '治疗部位',
    COUNT(*) as '使用次数'
FROM treatment_details 
GROUP BY body_part
ORDER BY COUNT(*) DESC;

-- 显示最近患者信息（新建档案界面数据预览）
SELECT '=== 最近患者信息（新建档案界面数据） ===' as info;
SELECT 
    p.patient_card_id as '就诊卡号',
    p.name as '姓名',
    p.age as '年龄',
    p.gender as '性别',
    DATE_FORMAT(MAX(pr.start_time), '%Y.%m.%d') as '最后治疗日期',
    GROUP_CONCAT(DISTINCT td.body_part SEPARATOR ', ') as '治疗部位',
    COUNT(DISTINCT pr.id) as '会话次数'
FROM patients p
LEFT JOIN records r ON p.id = r.patient_id
LEFT JOIN processes pr ON r.id = pr.record_id AND pr.status = 'COMPLETED'
LEFT JOIN treatment_details td ON pr.id = td.process_id
WHERE p.id <= 10
GROUP BY p.id, p.patient_card_id, p.name, p.age, p.gender
ORDER BY MAX(pr.start_time) DESC;

-- 显示进程管理界面数据
SELECT '=== 进程管理界面数据 ===' as info;
SELECT 
    p.patient_card_id as '患者卡号',
    p.name as '患者姓名',
    GROUP_CONCAT(DISTINCT td.body_part SEPARATOR ', ') as '治疗部位',
    pr.status as '状态',
    CASE pr.status
        WHEN 'IN_PROGRESS' THEN '正在治疗'
        WHEN 'COMPLETED' THEN '治疗完成'
        WHEN 'CANCELLED' THEN '已取消'
    END as '状态描述'
FROM processes pr
JOIN records r ON pr.record_id = r.id
JOIN patients p ON r.patient_id = p.id
LEFT JOIN treatment_details td ON pr.id = td.process_id
GROUP BY pr.id, p.patient_card_id, p.name, pr.status
ORDER BY pr.start_time DESC
LIMIT 15;