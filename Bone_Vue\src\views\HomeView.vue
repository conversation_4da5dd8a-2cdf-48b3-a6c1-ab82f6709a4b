<script setup lang="ts">
import { ref } from 'vue';
import { MessagePlugin } from 'tdesign-vue-next';
import { useRouter } from 'vue-router';
import { onMounted } from 'vue';
import http from '@/utils/axios';

const router = useRouter();

// 治疗头状态数据
const availableHeads = ref(0);
const totalHeads = ref(0);

// 获取治疗头状态
const fetchTreatmentHeadStatus = async () => {
  try {
    const response = await http.get('/api/treatment-heads');
    let heads = [];
    if (Array.isArray(response.data)) {
      heads = response.data;
    } else if (response.data.content && Array.isArray(response.data.content)) {
      heads = response.data.content;
    }
    
    totalHeads.value = heads.length;
    availableHeads.value = heads.filter((head: any) => head.status === 'AVAILABLE').length;
  } catch (error) {
    console.error('获取治疗头状态失败:', error);
    // 默认值
    totalHeads.value = 20;
    availableHeads.value = 20;
  }
};

// 跳转到新建患者档案页面
const goToNewPatient = () => {
  router.push('/new-patient');
};

// 跳转到患者档案管理页面
const goToPatientManagement = () => {
  router.push('/patients');
};

// 跳转到进程管理页面
const goToProcessManagement = () => {
  router.push('/process-management');
};

// 跳转到治疗头管理页面
const goToTreatmentHeadManagement = () => {
  router.push('/treatment-head-management');
};

// 退出登录
const logout = () => {
  localStorage.removeItem('isAuthenticated');
  MessagePlugin.success('已退出登录');
  router.push('/login');
};

// 页面加载时获取治疗头状态
onMounted(() => {
  fetchTreatmentHeadStatus();
});
</script>

<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="box_1 flex-col">
        <!-- 左上角返回按钮 -->
        
        <div class="back-button flex-row justify-between" @click="logout">
            <span class="text_31">返回登录</span>
          </div>
        
        <!-- 右上角治疗头状态 -->
        <div class="treatment-status" @click="goToTreatmentHeadManagement">
          <span>{{ availableHeads }}/{{ totalHeads }}</span>
        </div>
        
        <img
          class="image_1"
          referrerpolicy="no-referrer"
          src="../assets/images/logo.png"
        />
        <div class="image-wrapper_1 flex-row justify-between">
          <div class="function-button-container">
            <img
              class="image_2"
              referrerpolicy="no-referrer"
              src="../assets/images/new_patient.png"
              @click="goToNewPatient"
            />
          </div>
          <div class="function-button-container">
            <img
              class="image_3"
              referrerpolicy="no-referrer"
              src="../assets/images/patient_management.png"
              @click="goToPatientManagement"
            />
          </div>
        </div>
        <div class="function-button-container center">
          <img
            class="image_4"
            referrerpolicy="no-referrer"
            src="../assets/images/process_management.png"
            @click="goToProcessManagement"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped>
.page {
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  height: 1080px;
  background: url('../assets/images/background.png')
    100% no-repeat;
  background-size: 100% 100%;
  width: 1920px;
}

.box_1 {
  width: 1920px;
  height: 1080px;
  background: url('../assets/images/main_background.png')
    0px 0px no-repeat;
  background-size: 1920px 1083px;
  position: relative;
}

.back-button {
  position: absolute;
  top: 10px;
  left: 100px;
  cursor: pointer;
  z-index: 10;
  transition: all 0.3s ease;
  width: 241px;
  height: 63px;
  background: url(../assets/images/return.png)
    0px 0px no-repeat;
  background-size: 241.0699999999997px 63.22000000000003px;
  margin: 0px 0 0 6px;
}

.group_11 {
  width: 33px;
  height: 30px;
  margin: 16px 0 0 20px;
}

.text_31 {
  width: 152px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 95px;
  margin: -15px 20px 0 70px;
}
.back-button:hover {
  transform: scale(1.05);
}

.treatment-status {
  position: absolute;
  top: 30px;
  right: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #000000;
  font-size: 30px;
  font-weight: bold;
  z-index: 10;
  cursor: pointer;
}

.image_1 {
  width: 691px;
  height: 111px;
  margin: 155px 0 0 615px;
}

.image-wrapper_1 {
  width: 1304px;
  height: 462px;
  margin: 148px 0 0 271px;
  position: relative;
}

.function-button-container {
  position: relative;
  overflow: visible;
  transition: all 0.3s ease;
}

.function-button-container:hover {
  transform: translateY(-8px);
}

.function-button-container:active {
  transform: translateY(2px);
}

.function-button-container.center {
  margin: 5px 0 62px 786px;
  width: 346px;
}

.image_2 {
  width: 663px;
  height: 462px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_2:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

.image_3 {
  width: 611px;
  height: 420px;
  margin-top: 18px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_3:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

.image_4 {
  width: 346px;
  height: 137px;
  cursor: pointer;
  filter: drop-shadow(0 10px 15px rgba(0,0,0,0.3));
  transition: all 0.3s ease;
}

.image_4:hover {
  filter: drop-shadow(0 15px 25px rgba(0,0,0,0.5)) brightness(1.05);
}

/* 通用样式 */
.flex-col {
  display: flex;
  flex-direction: column;
}

.flex-row {
  display: flex;
  flex-direction: row;
}

.justify-between {
  justify-content: space-between;
}
</style>
