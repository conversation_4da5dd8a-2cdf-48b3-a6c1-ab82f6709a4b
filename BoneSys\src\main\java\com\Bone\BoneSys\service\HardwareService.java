package com.Bone.BoneSys.service;

import com.Bone.BoneSys.dto.hardware.*;
import com.Bone.BoneSys.entity.TreatmentHead;
import com.Bone.BoneSys.entity.enums.TreatmentHeadStatus;
import com.Bone.BoneSys.exception.SerialCommunicationException;
import com.Bone.BoneSys.repository.TreatmentHeadRepository;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import jakarta.annotation.PostConstruct;

import java.util.List;
import java.util.Optional;

/**
 * 硬件服务
 * 提供硬件操作的高级业务接口，整合串口通信和数据库操作
 */
@Service
@Transactional
public class HardwareService {
    
    private static final Logger logger = LoggerFactory.getLogger(HardwareService.class);
    
    @Autowired
    private SerialCommunicationService serialCommunicationService;
    
    @Autowired
    private HardwareCommandParser commandParser;
    
    @Autowired
    private TreatmentHeadRepository treatmentHeadRepository;
    
    /**
     * 应用启动时初始化治疗头数据
     */
    @PostConstruct
    public void initializeTreatmentHeads() {
        try {
            logger.info("Initializing treatment heads data on application startup");
            syncAllTreatmentHeads();
            logger.info("Treatment heads data initialization completed successfully");
        } catch (Exception e) {
            logger.warn("Failed to initialize treatment heads data on startup: {}", e.getMessage());
            // 不抛出异常，避免影响应用启动
        }
    }
    
    /**
     * 同步所有治疗头数据
     * 从硬件查询最新数据并更新数据库
     */
    public List<TreatmentHeadInfo> syncAllTreatmentHeads() throws SerialCommunicationException {
        logger.info("Starting to sync all treatment heads data from hardware");
        
        try {
            // 1. 构建查询指令
            String command = commandParser.buildQueryAllTreatmentHeadsCommand();
            
            // 2. 发送指令到硬件
            String response = serialCommunicationService.sendCommand(command);
            
            // 3. 解析硬件响应
            List<TreatmentHeadInfo> hardwareHeadInfos = commandParser.parseQueryAllTreatmentHeadsResponse(response);
            
            // 4. 更新数据库中的治疗头信息
            updateTreatmentHeadsInDatabase(hardwareHeadInfos);
            
            logger.info("Successfully synced {} treatment heads from hardware", hardwareHeadInfos.size());
            return hardwareHeadInfos;
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to sync treatment heads from hardware", e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during treatment heads sync", e);
            throw new SerialCommunicationException("Failed to sync treatment heads", e);
        }
    }
    
    /**
     * 设置治疗头指示灯
     */
    public List<TreatmentHeadLightResponse> setTreatmentHeadLights(List<TreatmentHeadLightRequest> lightRequests) 
            throws SerialCommunicationException {
        logger.info("Setting lights for {} treatment heads", lightRequests.size());
        
        try {
            // 1. 验证请求参数
            validateLightRequests(lightRequests);
            
            // 2. 构建点灯指令
            String command = commandParser.buildLightUpCommand(lightRequests);
            
            // 3. 发送指令到硬件
            String response = serialCommunicationService.sendCommand(command);
            
            // 4. 解析硬件响应
            List<TreatmentHeadLightResponse> lightResponses = commandParser.parseLightUpResponse(response);
            
            // 5. 更新数据库中的指示灯状态
            updateLightStatusInDatabase(lightResponses);
            
            logger.info("Successfully set lights for {} treatment heads", lightResponses.size());
            return lightResponses;
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to set treatment head lights", e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during light setting", e);
            throw new SerialCommunicationException("Failed to set treatment head lights", e);
        }
    }
    
    /**
     * 关闭治疗头指示灯
     */
    public List<Integer> turnOffTreatmentHeadLights(List<Integer> headNumbers) throws SerialCommunicationException {
        logger.info("Turning off lights for treatment heads: {}", headNumbers);
        
        try {
            // 1. 验证治疗头编号
            validateHeadNumbers(headNumbers);
            
            // 2. 构建关灯指令
            String command = commandParser.buildTurnOffLightCommand(headNumbers);
            
            // 3. 发送指令到硬件
            String response = serialCommunicationService.sendCommand(command);
            
            // 4. 解析硬件响应
            List<Integer> responseHeadNumbers = commandParser.parseTurnOffLightResponse(response);
            
            // 5. 更新数据库中的指示灯状态
            updateLightOffStatusInDatabase(responseHeadNumbers);
            
            logger.info("Successfully turned off lights for treatment heads: {}", responseHeadNumbers);
            return responseHeadNumbers;
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to turn off treatment head lights", e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during light turn off", e);
            throw new SerialCommunicationException("Failed to turn off treatment head lights", e);
        }
    }
    
    /**
     * 发送治疗参数到治疗头（不开始治疗）
     */
    public boolean sendTreatmentParams(TreatmentParamsRequest request) throws SerialCommunicationException {
        logger.info("Sending treatment params: duration={}min, intensity={}mW/cm², frequency={}Hz to heads: {}", 
                   request.getDuration(), request.getIntensity(), request.getFrequency(), request.getHeadNumbers());
        
        try {
            // 1. 验证治疗参数
            if (!request.isValid()) {
                throw new SerialCommunicationException("Invalid treatment parameters: " + request);
            }
            
            // 2. 验证治疗头是否可用
            validateTreatmentHeadsAvailable(request.getHeadNumbers());
            
            // 3. 构建参数发送指令
            String command = commandParser.buildSendTreatmentParamsCommand(request);
            
            // 4. 发送指令到硬件
            String response = serialCommunicationService.sendCommand(command);
            
            // 5. 验证响应（使用专门的验证方法）
            boolean success = commandParser.validateSendTreatmentParamsResponse(response, request);
            
            if (success) {
                logger.info("Successfully sent treatment params to heads: {}", request.getHeadNumbers());
            } else {
                logger.warn("Treatment params response validation failed: {}", response);
            }
            
            return success;
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to send treatment params", e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during treatment params sending", e);
            throw new SerialCommunicationException("Failed to send treatment params", e);
        }
    }
    
    /**
     * 开始治疗（发送参数并启动）
     */
    public boolean startTreatment(int headNumber, int duration, int intensity, int frequency) 
            throws SerialCommunicationException {
        logger.info("Starting treatment on head {} with params: duration={}min, intensity={}mW/cm², frequency={}Hz", 
                   headNumber, duration, intensity, frequency);
        
        try {
            // 1. 验证治疗头是否可用
            validateTreatmentHeadAvailable(headNumber);
            
            // 2. 构建开始治疗指令
            String command = commandParser.buildStartTreatmentCommand(headNumber, duration, intensity, frequency);
            
            // 3. 发送指令到硬件
            String response = serialCommunicationService.sendCommand(command);
            
            // 4. 验证响应（使用专门的验证方法）
            boolean success = commandParser.validateStartTreatmentResponse(response, headNumber, duration, intensity, frequency);
            
            if (success) {
                // 5. 更新数据库中的治疗头状态为治疗中
                updateTreatmentHeadStatus(headNumber, TreatmentHeadStatus.TREATING);
                logger.info("Successfully started treatment on head {}", headNumber);
            } else {
                logger.warn("Start treatment response validation failed: {}", response);
            }
            
            return success;
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to start treatment on head {}", headNumber, e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during treatment start", e);
            throw new SerialCommunicationException("Failed to start treatment", e);
        }
    }
    
    /**
     * 停止治疗
     */
    public boolean stopTreatment(int headNumber) throws SerialCommunicationException {
        logger.info("Stopping treatment on head {}", headNumber);
        
        try {
            // 1. 构建停止治疗指令
            String command = commandParser.buildStopTreatmentCommand(headNumber);
            
            // 2. 发送指令到硬件
            String response = serialCommunicationService.sendCommand(command);
            
            // 3. 解析响应
            int responseHeadNumber = commandParser.parseStopTreatmentResponse(response);
            
            boolean success = (responseHeadNumber == headNumber);
            
            if (success) {
                // 4. 更新数据库中的治疗头状态
                updateTreatmentHeadStatus(headNumber, TreatmentHeadStatus.CHARGED);
                logger.info("Successfully stopped treatment on head {}", headNumber);
            } else {
                logger.warn("Head number mismatch in stop response. Expected: {}, Got: {}", headNumber, responseHeadNumber);
            }
            
            return success;
            
        } catch (SerialCommunicationException e) {
            logger.error("Failed to stop treatment on head {}", headNumber, e);
            throw e;
        } catch (Exception e) {
            logger.error("Unexpected error during treatment stop", e);
            throw new SerialCommunicationException("Failed to stop treatment", e);
        }
    }
    
    /**
     * 检查硬件连接状态
     */
    public boolean isHardwareConnected() {
        return serialCommunicationService.isConnected();
    }
    
    /**
     * 重新连接硬件
     */
    public void reconnectHardware() throws SerialCommunicationException {
        logger.info("Reconnecting to hardware");
        serialCommunicationService.reconnect();
    }
    
    /**
     * 获取硬件连接信息
     */
    public String getHardwareInfo() {
        // return serialCommunicationService.getConnectionInfo();
        return null; // TODO: Implement connection info
    }
    
    /**
     * 获取可用串口列表
     */
    public String[] getAvailablePorts() {
        // return EnhancedSerialCommunicationService.getAvailablePorts();
        return new String[]{"COM1", "COM2", "COM3"}; // TODO: Implement port detection
    }
    
    /**
     * 测试硬件连接
     */
    public boolean testHardwareConnection() {
        return serialCommunicationService.isConnected();
    }
    
    /**
     * 查询治疗头状态（便捷方法）
     */
    public List<TreatmentHeadInfo> queryTreatmentHeads() throws SerialCommunicationException {
        return syncAllTreatmentHeads();
    }
    
    /**
     * 点亮治疗头指示灯（便捷方法）
     */
    public List<TreatmentHeadLightResponse> lightUpTreatmentHeads(List<TreatmentHeadLightRequest> lightRequests) 
            throws SerialCommunicationException {
        return setTreatmentHeadLights(lightRequests);
    }
    
    /**
     * 关闭治疗头指示灯（便捷方法）
     */
    public List<Integer> lightOffTreatmentHeads(List<Integer> headNumbers) throws SerialCommunicationException {
        return turnOffTreatmentHeadLights(headNumbers);
    }
    
    /**
     * 下载参数到治疗头（便捷方法）
     */
    public boolean downloadParameters(TreatmentParamsRequest request) throws SerialCommunicationException {
        return sendTreatmentParams(request);
    }
    
    // ==================== 私有辅助方法 ====================
    
    /**
     * 更新数据库中的治疗头信息
     */
    private void updateTreatmentHeadsInDatabase(List<TreatmentHeadInfo> hardwareHeadInfos) {
        for (TreatmentHeadInfo headInfo : hardwareHeadInfos) {
            Optional<TreatmentHead> optionalHead = treatmentHeadRepository.findByHeadNumber(headInfo.getHeadNumber());
            
            TreatmentHead head;
            if (optionalHead.isPresent()) {
                // 更新现有记录
                head = optionalHead.get();
                logger.debug("Updating existing treatment head {} in database", headInfo.getHeadNumber());
            } else {
                // 创建新记录
                head = new TreatmentHead();
                head.setHeadNumber(headInfo.getHeadNumber());
                head.setMaxUsageCount(1000); // 默认最大使用次数
                head.setLightColor(0); // 默认关闭
                head.setTotalUsageMinutes(0); // 默认使用时间
                logger.debug("Creating new treatment head {} in database", headInfo.getHeadNumber());
            }
            
            // 更新/设置通用属性
            head.setBatteryLevel(headInfo.getBatteryLevel());
            head.setTotalUsageCount(headInfo.getUsageCount());
            head.setSlotNumber(headInfo.getSlotNumber());
            
            // 根据硬件状态设置数据库状态
            if ("CHARGING".equals(headInfo.getStatus())) {
                head.setRealtimeStatus(TreatmentHeadStatus.CHARGING);
            } else if ("CHARGED".equals(headInfo.getStatus())) {
                head.setRealtimeStatus(TreatmentHeadStatus.CHARGED);
            } else {
                head.setRealtimeStatus(TreatmentHeadStatus.CHARGED); // 默认状态
            }
            
            treatmentHeadRepository.save(head);
            logger.debug("Successfully saved treatment head {} to database", headInfo.getHeadNumber());
        }
    }
    
    /**
     * 更新数据库中的指示灯状态
     */
    private void updateLightStatusInDatabase(List<TreatmentHeadLightResponse> lightResponses) {
        for (TreatmentHeadLightResponse response : lightResponses) {
            Optional<TreatmentHead> optionalHead = treatmentHeadRepository.findByHeadNumber(response.getHeadNumber());
            
            if (optionalHead.isPresent()) {
                TreatmentHead head = optionalHead.get();
                head.setLightColor(response.getColorCode());
                treatmentHeadRepository.save(head);
                logger.debug("Updated light color for head {} to {}", response.getHeadNumber(), response.getColorCode());
            }
        }
    }
    
    /**
     * 更新数据库中的指示灯关闭状态
     */
    private void updateLightOffStatusInDatabase(List<Integer> headNumbers) {
        for (Integer headNumber : headNumbers) {
            Optional<TreatmentHead> optionalHead = treatmentHeadRepository.findByHeadNumber(headNumber);
            
            if (optionalHead.isPresent()) {
                TreatmentHead head = optionalHead.get();
                head.setLightColor(0); // 0表示关闭
                treatmentHeadRepository.save(head);
                logger.debug("Turned off light for head {}", headNumber);
            }
        }
    }
    
    /**
     * 更新治疗头状态
     */
    private void updateTreatmentHeadStatus(int headNumber, TreatmentHeadStatus status) {
        Optional<TreatmentHead> optionalHead = treatmentHeadRepository.findByHeadNumber(headNumber);
        
        if (optionalHead.isPresent()) {
            TreatmentHead head = optionalHead.get();
            head.setRealtimeStatus(status);
            treatmentHeadRepository.save(head);
            logger.debug("Updated status for head {} to {}", headNumber, status);
        }
    }
    
    /**
     * 验证指示灯请求参数
     */
    private void validateLightRequests(List<TreatmentHeadLightRequest> lightRequests) throws SerialCommunicationException {
        if (lightRequests == null || lightRequests.isEmpty()) {
            throw new SerialCommunicationException("Light requests cannot be empty");
        }
        
        if (lightRequests.size() > 20) {
            throw new SerialCommunicationException("Too many light requests: " + lightRequests.size());
        }
        
        for (TreatmentHeadLightRequest request : lightRequests) {
            if (request.getHeadNumber() < 1 || request.getHeadNumber() > 20) {
                throw new SerialCommunicationException("Invalid head number: " + request.getHeadNumber());
            }
            
            if (request.getColorCode() < 0 || request.getColorCode() > 3) {
                throw new SerialCommunicationException("Invalid color code: " + request.getColorCode());
            }
        }
    }
    
    /**
     * 验证治疗头编号列表
     */
    private void validateHeadNumbers(List<Integer> headNumbers) throws SerialCommunicationException {
        if (headNumbers == null || headNumbers.isEmpty()) {
            throw new SerialCommunicationException("Head numbers cannot be empty");
        }
        
        for (Integer headNumber : headNumbers) {
            if (headNumber < 1 || headNumber > 20) {
                throw new SerialCommunicationException("Invalid head number: " + headNumber);
            }
        }
    }
    
    /**
     * 验证单个治疗头是否可用
     */
    private void validateTreatmentHeadAvailable(int headNumber) throws SerialCommunicationException {
        if (headNumber < 1 || headNumber > 20) {
            throw new SerialCommunicationException("Invalid head number: " + headNumber);
        }
        
        Optional<TreatmentHead> optionalHead = treatmentHeadRepository.findByHeadNumber(headNumber);
        if (!optionalHead.isPresent()) {
            throw new SerialCommunicationException("Treatment head not found: " + headNumber);
        }
        
        TreatmentHead head = optionalHead.get();
        if (head.getRealtimeStatus() == TreatmentHeadStatus.TREATING) {
            throw new SerialCommunicationException("Treatment head is already in use: " + headNumber);
        }
        
        if (head.getBatteryLevel() != null && head.getBatteryLevel() < 60) {
            throw new SerialCommunicationException("Treatment head battery too low (< 60%): " + headNumber);
        }
    }
    
    /**
     * 处理串口通信异常并尝试重连
     */
    private void handleCommunicationError(SerialCommunicationException e) throws SerialCommunicationException {
        logger.warn("Communication error occurred: {}", e.getMessage());
        
        // 如果是连接相关的错误，尝试重连
        if (e.getMessage().contains("not connected") || e.getMessage().contains("connection")) {
            try {
                logger.info("Attempting to reconnect due to communication error...");
                serialCommunicationService.reconnect();
                logger.info("Reconnection successful");
            } catch (Exception reconnectEx) {
                logger.error("Failed to reconnect after communication error", reconnectEx);
                throw new SerialCommunicationException("Communication failed and reconnection unsuccessful", reconnectEx);
            }
        }
        
        throw e;
    }
    
    /**
     * 验证多个治疗头是否可用
     */
    private void validateTreatmentHeadsAvailable(List<Integer> headNumbers) throws SerialCommunicationException {
        for (Integer headNumber : headNumbers) {
            validateTreatmentHeadAvailable(headNumber);
        }
    }
}