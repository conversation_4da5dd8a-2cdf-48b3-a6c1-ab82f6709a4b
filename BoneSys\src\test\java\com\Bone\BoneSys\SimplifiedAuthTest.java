package com.Bone.BoneSys;

import com.Bone.BoneSys.util.PasswordUtil;
import org.junit.jupiter.api.Test;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 简化认证系统测试
 */
public class SimplifiedAuthTest {

    @Test
    void testPasswordValidation() {
        System.out.println("🔐 测试简化密码验证系统");
        
        // 测试默认密码
        assertTrue(PasswordUtil.isDefaultPassword("admin123"), "admin123应该是默认密码");
        assertTrue(PasswordUtil.isDefaultPassword("factory123"), "factory123应该是默认密码");
        assertFalse(PasswordUtil.isDefaultPassword("wrongpassword"), "错误密码不应该是默认密码");
        
        // 测试密码强度
        assertTrue(PasswordUtil.isValidPassword("admin123"), "admin123应该是有效密码");
        assertTrue(PasswordUtil.isValidPassword("123456"), "123456应该是有效密码");
        assertFalse(PasswordUtil.isValidPassword("123"), "123太短，不应该是有效密码");
        assertFalse(PasswordUtil.isValidPassword(""), "空密码不应该是有效密码");
        assertFalse(PasswordUtil.isValidPassword(null), "null密码不应该是有效密码");
        
        System.out.println("✅ 密码验证测试通过");
    }
    
    @Test
    void testPasswordConstants() {
        System.out.println("🔧 测试密码常量");
        
        assertEquals("admin123", PasswordUtil.DEFAULT_USER_PASSWORD, "默认用户密码应该是admin123");
        assertEquals("factory123", PasswordUtil.DEFAULT_FACTORY_PASSWORD, "默认厂家密码应该是factory123");
        
        String hint = PasswordUtil.getPasswordHint();
        assertTrue(hint.contains("admin123"), "密码提示应该包含用户密码");
        assertTrue(hint.contains("factory123"), "密码提示应该包含厂家密码");
        
        System.out.println("密码提示: " + hint);
        System.out.println("✅ 密码常量测试通过");
    }
    
    @Test
    void testLoginScenarios() {
        System.out.println("🎯 测试登录场景");
        
        System.out.println("=== 首次登录场景 ===");
        System.out.println("- factory123: 应该成功（厂家密码）");
        System.out.println("- admin123: 应该失败（用户密码，但首次登录只能用厂家密码）");
        System.out.println("- wrongpassword: 应该失败（错误密码）");
        
        System.out.println();
        System.out.println("=== 设置用户密码后的登录场景 ===");
        System.out.println("- 用户设置的密码: 应该成功");
        System.out.println("- factory123: 应该失败（厂家密码不能用于日常登录）");
        System.out.println("- 其他密码: 应该失败");
        
        System.out.println();
        System.out.println("=== 密码重置场景 ===");
        System.out.println("- 使用厂家密码重置: 应该成功");
        System.out.println("- 使用错误厂家密码: 应该失败");
        
        System.out.println();
        System.out.println("=== 密码修改场景 ===");
        System.out.println("- 使用当前用户密码修改: 应该成功");
        System.out.println("- 使用错误的旧密码: 应该失败");
        
        System.out.println("✅ 登录场景测试完成");
    }
}