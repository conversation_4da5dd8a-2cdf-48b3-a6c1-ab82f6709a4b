<template>
    <div class="page flex-col">
      <div class="box_1 flex-col">
        <div class="group_1 flex-row justify-between">
          <img
            class="image_1"
            referrerpolicy="no-referrer"
            src="../assets/images/treatmentprocess/bcc19f244b85ecf170b3f9e6621a2893.png"
            @click="goBack"
          />
          <span class="text_1">治疗进程</span>
        </div>
        <div class="group_2 flex-row">
          <img
            class="image_2"
            referrerpolicy="no-referrer"
            src="../assets/images/treatmentprocess/9541918c005334599a1dd4a8efde1d65.png"
          />
          <span class="text_2">姓名：{{ patientName }}</span>
          <div class="block_1 flex-row button" @click="goToNewRecord">
            <img
              class="label_1"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/0edae6f606d6f1ab381b509e03470848.png"
            />
            <span class="text_3">返回个人新建</span>
          </div>
          <div class="block_2 flex-row button" @click="goToProcessManagement">
            <img
              class="label_2"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/9d65acadec38caf75208af40ada7710f.png"
            />
            <span class="text_4">查看进程管理</span>
          </div>
          <div class="block_3 flex-row button" @click="closeAllTreatmentHeads">
            <img
              class="label_3"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/71054227f9f833ec4c2433447f86b19d.png"
            />
            <span class="text_6">结束</span>
          </div>
        </div>
        <div class="group_3 flex-row">
          <!-- 第一个治疗卡片 -->
          <div class="treatment-card card-1 flex-col" v-if="treatmentHeads.length > 0">
            <div class="card-header flex-row">
              <div class="image-text flex-row justify-between">
                <img
                  class="label"
                  referrerpolicy="no-referrer"
                  src="../assets/images/treatmentprocess/132e26419509ac28402daef983020f0b.png"
                />
                <span class="text-group">{{ treatmentHeads[0]?.bodyPart }}</span>
              </div>
              <img
                class="close-button"
                referrerpolicy="no-referrer"
                src="../assets/images/treatmentprocess/9571c283df7c05fad59c3c38870d9f27.png"
                @click="closeTreatmentHead(treatmentHeads[0]?.id)"
              />
            </div>
            <div class="time-row flex-row justify-between">
              <span class="text_7">剩余时间</span>
              <span class="text_8">{{ formatTime(treatmentHeads[0]?.remainingTimeSeconds) }}</span>
            </div>
            <img
              class="divider"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/3cd2eb009c1851efb888208425b4bce6.png"
            />
            <div class="intensity-row flex-row justify-between">
              <span class="text_9">治疗声强</span>
              <span class="text_10">{{ treatmentHeads[0]?.intensity }}</span>
            </div>
          </div>
          
          <!-- 第二个治疗卡片 -->
          <div class="treatment-card card-2 flex-col" v-if="treatmentHeads.length > 1">
            <div class="card-header flex-row">
              <div class="image-text flex-row justify-between">
                <img
                  class="label"
                  referrerpolicy="no-referrer"
                  src="../assets/images/treatmentprocess/4d2380739eaf26b34098692cef647123.png"
                />
                <span class="text-group">{{ treatmentHeads[1]?.bodyPart }}</span>
              </div>
              <img
                class="close-button"
                referrerpolicy="no-referrer"
                src="../assets/images/treatmentprocess/9571c283df7c05fad59c3c38870d9f27.png"
                @click="closeTreatmentHead(treatmentHeads[1]?.id)"
              />
            </div>
            <div class="time-row flex-row justify-between">
              <span class="text_7">剩余时间</span>
              <span class="text_8">{{ formatTime(treatmentHeads[1]?.remainingTimeSeconds) }}</span>
            </div>
            <img
              class="divider"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/3cd2eb009c1851efb888208425b4bce6.png"
            />
            <div class="intensity-row flex-row justify-between">
              <span class="text_9">治疗声强</span>
              <span class="text_10">{{ treatmentHeads[1]?.intensity }}</span>
            </div>
          </div>
          
          <!-- 第三个治疗卡片 -->
          <div class="treatment-card card-3 flex-col" v-if="treatmentHeads.length > 2">
            <div class="card-header flex-row">
              <div class="image-text flex-row justify-between">
                <img
                  class="label"
                  referrerpolicy="no-referrer"
                  src="../assets/images/treatmentprocess/5c5b9f1cb0725dfcc3fbb73b3fbc0685.png"
                />
                <span class="text-group">{{ treatmentHeads[2]?.bodyPart }}</span>
              </div>
              <img
                class="close-button"
                referrerpolicy="no-referrer"
                src="../assets/images/treatmentprocess/9571c283df7c05fad59c3c38870d9f27.png"
                @click="closeTreatmentHead(treatmentHeads[2]?.id)"
              />
            </div>
            <div class="time-row flex-row justify-between">
              <span class="text_7">剩余时间</span>
              <span class="text_8">{{ formatTime(treatmentHeads[2]?.remainingTimeSeconds) }}</span>
            </div>
            <img
              class="divider"
              referrerpolicy="no-referrer"
              src="../assets/images/treatmentprocess/3cd2eb009c1851efb888208425b4bce6.png"
            />
            <div class="intensity-row flex-row justify-between">
              <span class="text_9">治疗声强</span>
              <span class="text_10">{{ treatmentHeads[2]?.intensity }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </template>
  
  <script setup lang="ts">
  import { ref, onMounted, onUnmounted } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import http from '@/utils/axios';
  
  const router = useRouter();
  const route = useRoute();
  
  // 患者信息
  const patientName = ref('张三'); // 根据就诊卡号查询获取
  const medicalRecordId = ref(''); // 就诊卡号
  
  // 治疗头数据状态
  interface TreatmentHead {
    id: string;
    headNumber: string;  // 治疗头编号
    bodyPart: string;    // 对应的治疗部位
    remainingTimeSeconds: number;  // 剩余时间（秒）
    intensity: string;   // 治疗声强
    isActive: boolean;   // 是否活跃
  }
  
  const treatmentHeads = ref<TreatmentHead[]>([]);
  
  // 根据治疗设置生成治疗头数据
  const generateTreatmentHeadsFromSettings = () => {
    // 模拟从治疗设置页面传递的数据或从localStorage获取
    // 实际项目中应该从路由参数、全局状态或后端接口获取
    const mockTreatmentSettings = [
      {
        name: '肩颈部',
        time: '1分钟',
        intensity: '150',
        frequency: '1000',
        depth: '深部',
        count: '1'  // 用户选择的治疗头数量 (改为1)
      },
      {
        name: '腰背部',
        time: '12分钟',
        intensity: '180',
        frequency: '1000',
        depth: '深部',
        count: '1'  // 用户选择的治疗头数量 (改为1)
      },
      {
        name: '下肢',
        time: '8分钟',
        intensity: '160',
        frequency: '100',
        depth: '浅部',
        count: '1'  // 用户选择的治疗头数量 (改为1)
      }
    ];
  
    const treatmentHeads: TreatmentHead[] = [];
    let headCounter = 1;
  
    mockTreatmentSettings.forEach(setting => {
      const count = parseInt(setting.count) || 1;
      const timeInMinutes = parseInt(setting.time) || 15;
      const timeInSeconds = timeInMinutes * 60 + Math.floor(Math.random() * 60); // 添加随机秒数
  
      // 根据选择的数量生成对应数量的治疗头
      for (let i = 0; i < count; i++) {
        treatmentHeads.push({
          id: headCounter.toString(),
          headNumber: headCounter.toString().padStart(2, '0'),
          bodyPart: setting.name,
          remainingTimeSeconds: timeInSeconds,
          intensity: `${setting.intensity}mW/cm²`,
          isActive: true
        });
        headCounter++;
      }
    });
  
    return treatmentHeads;
  };
  
  // 获取患者信息
  const fetchPatientInfo = async (medicalRecordId: string) => {
    try {
      // 实际项目中应该调用后端接口获取患者信息
      // 示例: const response = await http.get(`/api/patients/${medicalRecordId}`);
      // 模拟数据
      patientName.value = '张三'; // 实际应从response.data.name获取
    } catch (error) {
      console.error('获取患者信息失败:', error);
      patientName.value = '未知患者';
    }
  };
  
  // 倒计时定时器
  let countdownTimer: number | null = null;
  
  // 初始化治疗数据
  const initializeTreatmentData = async () => {
    // 从路由参数获取就诊卡号
    medicalRecordId.value = route.params.medicalRecordId as string || '';
    
    // 根据就诊卡号查询患者信息
    if (medicalRecordId.value) {
      await fetchPatientInfo(medicalRecordId.value);
    }
    
    // 生成治疗头数据
    treatmentHeads.value = generateTreatmentHeadsFromSettings();
  };
  
  // 关闭指定治疗头
  const closeTreatmentHead = (headId: string) => {
    const index = treatmentHeads.value.findIndex(head => head.id === headId);
    if (index !== -1) {
      treatmentHeads.value[index].isActive = false;
      console.log(`关闭治疗头: ${treatmentHeads.value[index].bodyPart}`);
      
      // 移除不活跃的治疗头
      treatmentHeads.value = treatmentHeads.value.filter(head => head.isActive);
    }
  };
  
  // 将秒数转换为MM分SS秒格式
  const formatTime = (seconds: number): string => {
    if (seconds === undefined) return '0分0秒';
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}分${remainingSeconds}秒`;
  };
  
  // 开始倒计时
  const startCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
    }
    
    countdownTimer = setInterval(() => {
      treatmentHeads.value.forEach(head => {
        if (head.isActive && head.remainingTimeSeconds > 0) {
          head.remainingTimeSeconds--;
          
          // 当倒计时到0时
          if (head.remainingTimeSeconds <= 0) {
            console.log(`治疗头 ${head.bodyPart} 治疗完成`);
            // 这里可以添加治疗完成的处理逻辑
          }
        }
      });
      
      // 检查是否所有治疗头都完成了
      const activeHeads = treatmentHeads.value.filter(head => head.isActive && head.remainingTimeSeconds > 0);
      if (activeHeads.length === 0) {
        stopCountdown();
        console.log('所有治疗头治疗完成');
      }
    }, 1000);
  };
  
  // 停止倒计时
  const stopCountdown = () => {
    if (countdownTimer) {
      clearInterval(countdownTimer);
      countdownTimer = null;
    }
  };
  
  // 返回上一页
  const goBack = () => {
    router.back();
  };
  
  // 跳转到新建档案
  const goToNewRecord = () => {
    router.push('/new-patient');
  };
  
  // 跳转到进程管理
  const goToProcessManagement = () => {
    router.push('/process-management');
  };
  
  // 关闭所有治疗头
  const closeAllTreatmentHeads = () => {
    console.log('关闭所有治疗头');
    treatmentHeads.value = [];
  };
  
  // 页面加载时初始化数据
  onMounted(() => {
    initializeTreatmentData().then(() => {
      startCountdown();
    });
  });
  
  // 页面卸载时清理定时器
  onUnmounted(() => {
    stopCountdown();
  });
  </script>
  
  <style scoped lang="css">
  .page {
    background-color: rgba(255, 255, 255, 1);
    position: relative;
    width: 1920px;
    height: 1080px;
    overflow: hidden;
  }
  
  .box_1 {
    height: 1080px;
    background: url(../assets/images/treatmentprocess/581596b6024a8797eb084676c601cc04.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin-left: 1px;
    width: 1919px;
  }
  
  .group_1 {
    width: 1003px;
    height: 70px;
    margin: 20px 0 0 103px;
  }
  
  .image_1 {
    width: 169px;
    height: 70px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .image_1:active {
    transform: scale(0.95);
  }
  
  .text_1 {
    width: 294px;
    height: 50px;
    overflow-wrap: break-word;
    color: rgba(1, 1, 1, 1);
    font-size: 50px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 10px;
  }
  
  .group_2 {
    position: relative;
    width: 1695px;
    height: 92px;
    margin: 77px 0 0 112px;
  }
  
  .image_2 {
    width: 56px;
    height: 56px;
    margin-top: 16px;
  }
  
  .text_2 {
    width: auto;
    height: 34px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 30px 0 0 24px;
  }
  
  .block_1 {
    width: 345px;
    height: 91px;
    background: url(../assets/images/treatmentprocess/da69ab339dafb93891d48a37c09b9a2f.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin-left: 500px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .block_1:active {
    transform: scale(0.95);
  }
  
  .label_1 {
    width: 32px;
    height: 29px;
    margin: 24px 0 0 25px;
  }
  
  .text_3 {
    width: 215px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 15px 49px 0 24px;
  }
  
  .block_2 {
    width: 345px;
    height: 91px;
    background: url(../assets/images/treatmentprocess/f328c43c1f4187c31ec25f2a47113077.png)
      100% no-repeat;
    background-size: 100% 100%;
    margin-left: 15px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .block_2:active {
    transform: scale(0.95);
  }
  
  .label_2 {
    width: 32px;
    height: 29px;
    margin: 24px 0 0 25px;
  }
  
  .text_4 {
    width: 215px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 15px 49px 0 24px;
  }
  
  .block_3 {
    position: absolute;
    left: 1468px;
    top: 1px;
    width: 227px;
    height: 91px;
    background: url(../assets/images/treatmentprocess/5e50169bee19e094657b18f2bc6e3b7a.png)
      100% no-repeat;
    background-size: 100% 100%;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .block_3:active {
    transform: scale(0.95);
  }
  
  .label_3 {
    width: 37px;
    height: 37px;
    margin: 20px 0 0 33px;
  }
  
  .text_6 {
    width: 70px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin: 15px 62px 0 25px;
  }
  
  .group_3 {
    width: 1699px;
    height: 279px;
    margin: 142px 0 400px 105px;
    display: flex;
    flex-direction: row;
    justify-content: flex-start;
    flex-wrap: nowrap;
    gap: 105px;
    overflow: hidden;
  }
  
  .treatment-card {
    box-shadow: 5px 9px 31px 6px rgba(29, 35, 36, 0.14);
    background-color: rgba(255, 255, 255, 1);
    width: 496px;
    height: 278px;
    border: 0.5px solid rgba(254, 152, 8, 1);
    flex-shrink: 0;
  }
  
  .card-1 {
    margin-top: 1px;
  }
  
  .card-2 {
    /* 通过 gap 控制间距 */
  }
  
  .card-3 {
    /* 通过 gap 控制间距 */
  }
  
  .card-header {
    width: 496px;
    height: 106px;
    background: url(../assets/images/treatmentprocess/8b04f3885006c864b55e5ac79f51715d.png)
      100% no-repeat;
    background-size: 100% 100%;
  }
  
  .image-text {
    width: 128px;
    height: 34px;
    margin: 30px 0 0 40px;
  }
  
  .label {
    width: 34px;
    height: 34px;
  }
  
  .text-group {
    width: 80px;
    height: 26px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 25px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: -2px;
    margin-left: 10px;
  }
  
  .close-button {
    width: 120px;
    height: 120px;
    margin: -10px 53px 0 224px;
    cursor: pointer;
    transition: transform 0.1s ease;
  }
  
  .close-button:active {
    transform: scale(0.9);
  }
  
  .time-row {
    width: 382px;
    height: 33px;
    margin: 46px 0 0 56px;
  }
  
  .text_7 {
    width: 141px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_8 {
    width: 159px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .divider {
    width: 408px;
    height: 3px;
    margin: 11px 0 0 48px;
  }
  
  .intensity-row {
    width: 411px;
    height: 33px;
    margin: 13px 0 33px 57px;
  }
  
  .text_9 {
    width: 142px;
    height: 33px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
  }
  
  .text_10 {
    width: 220px;
    height: 31px;
    overflow-wrap: break-word;
    color: rgba(89, 89, 89, 1);
    font-size: 33px;
    font-family: MicrosoftYaHei;
    font-weight: normal;
    text-align: center;
    white-space: nowrap;
    line-height: 41px;
    margin-top: 2px;
  }
  
  .button {
    transition: transform 0.1s ease;
  }
  
  .button:active {
    transform: scale(0.95);
  }
  
  .flex-row {
    display: flex;
    flex-direction: row;
  }
  
  .flex-col {
    display: flex;
    flex-direction: column;
  }
  
  .justify-between {
    justify-content: space-between;
  }
  </style>