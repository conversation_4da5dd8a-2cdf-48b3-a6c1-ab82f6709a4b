-- FREEBONE医疗设备管理系统 - 数据库编码修复脚本
-- 版本: 1.0
-- 日期: 2025-07-28
-- 用途: 修复数据库字符编码问题，解决中文乱码

-- 使用说明:
-- 1. 在修改前请备份数据库: mysqldump -u root -p bonesys > backup.sql
-- 2. 执行此脚本: mysql -u root -p bonesys < SQL/fix_encoding.sql
-- 3. 重新导入测试数据: mysql -u root -p --default-character-set=utf8mb4 bonesys < SQL/corrected_test_data.sql

-- ========================================
-- 第一步：修改数据库字符集
-- ========================================

-- 修改数据库默认字符集
ALTER DATABASE bonesys CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ========================================
-- 第二步：修改所有表的字符集
-- ========================================

-- 修改用户表
ALTER TABLE users CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改患者表
ALTER TABLE patients CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改档案表
ALTER TABLE records CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改治疗进程表
ALTER TABLE processes CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改治疗头表
ALTER TABLE treatment_heads CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改治疗详情表
ALTER TABLE treatment_details CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- 修改身体部位统计表
ALTER TABLE body_part_stats CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;

-- ========================================
-- 第三步：验证字符集修改结果
-- ========================================

-- 查看数据库字符集
SELECT DEFAULT_CHARACTER_SET_NAME, DEFAULT_COLLATION_NAME 
FROM information_schema.SCHEMATA 
WHERE SCHEMA_NAME = 'bonesys';

-- 查看所有表的字符集
SELECT TABLE_NAME, TABLE_COLLATION 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'bonesys';

-- 查看所有列的字符集（仅显示文本列）
SELECT TABLE_NAME, COLUMN_NAME, CHARACTER_SET_NAME, COLLATION_NAME 
FROM information_schema.COLUMNS 
WHERE TABLE_SCHEMA = 'bonesys' 
AND CHARACTER_SET_NAME IS NOT NULL;

-- ========================================
-- 第四步：测试中文数据
-- ========================================

-- 插入测试中文数据
INSERT INTO patients (patient_card_id, name, gender, age, contact_info, created_at) 
VALUES ('TEST001', '测试患者', '男', '30', '13800138000', NOW());

-- 查询测试数据
SELECT patient_card_id, name, gender, age, contact_info 
FROM patients 
WHERE patient_card_id = 'TEST001';

-- 清理测试数据
DELETE FROM patients WHERE patient_card_id = 'TEST001';

-- ========================================
-- 完成提示
-- ========================================

SELECT '数据库编码修复完成！' as message, 
       '请重新启动应用程序并测试中文显示' as next_step;

-- 注意事项:
-- 1. 修改完成后，请使用UTF-8编码重新导入测试数据
-- 2. 确保应用程序连接字符串包含正确的编码参数
-- 3. 如果仍有问题，请检查MySQL服务器配置文件