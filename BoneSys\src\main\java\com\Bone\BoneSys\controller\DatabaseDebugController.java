package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bonel.DataSource;
import java.sql.Connection;
import java.sql.ResultSet;
import java.sql.Statement;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

@RestController
@RequestMapping("/api/debug")
@CrossOrigin(origins = "*")
public class DatabaseDebugController {

    @Autowired
    private DataSource dataSource;

    @GetMapping("/table-structure")
    public ResponseEntity<Map<String, Object>> getTableStructure() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 查看treatment_heads表结构
            ResultSet rs = stmt.executeQuery("DESCRIBE treatment_heads");
            List<Map<String, Object>> columns = new ArrayList<>();
            
            while (rs.next()) {
                Map<String, Object> column = new HashMap<>();
                column.put("field", rs.getString("Field"));
                column.put("type", rs.getString("Type"));
                column.put("null", rs.getString("Null"));
                column.put("key", rs.getString("Key"));
                column.put("default", rs.getString("Default"));
                column.put("extra", rs.getString("Extra"));
                columns.add(column);
            }
            
            response.put("success", true);
            response.put("table", "treatment_heads");
            response.put("columns", columns);
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询表结构失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
    
    @GetMapping("/table-data")
    public ResponseEntity<Map<String, Object>> getTableData() {
        Map<String, Object> response = new HashMap<>();
        
        try (Connection conn = dataSource.getConnection();
             Statement stmt = conn.createStatement()) {
            
            // 查看treatment_heads表数据
            ResultSet rs = stmt.executeQuery("SELECT * FROM treatment_heads LIMIT 5");
            List<Map<String, Object>> rows = new ArrayList<>();
            
            while (rs.next()) {
                Map<String, Object> row = new HashMap<>();
                row.put("head_id", rs.getLong("head_id"));
                row.put("head_number", rs.getInt("head_number"));
                row.put("slot_number", rs.getObject("slot_number"));
                row.put("light_color", rs.getInt("light_color"));
                row.put("realtime_status", rs.getString("realtime_status"));
                row.put("battery_level", rs.getObject("battery_level"));
                row.put("total_usage_count", rs.getInt("total_usage_count"));
                row.put("total_usage_minutes", rs.getInt("total_usage_minutes"));
                row.put("max_usage_count", rs.getObject("max_usage_count"));
                rows.add(row);
            }
            
            response.put("success", true);
            response.put("table", "treatment_heads");
            response.put("rows", rows);
            response.put("count", rows.size());
            
        } catch (Exception e) {
            response.put("success", false);
            response.put("message", "查询表数据失败: " + e.getMessage());
            e.printStackTrace();
        }
        
        return ResponseEntity.ok(response);
    }
}