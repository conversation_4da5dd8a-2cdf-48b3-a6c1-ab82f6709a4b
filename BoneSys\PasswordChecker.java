import org.springframework.security.crypto.bcrypt.BCrypt;

/**
 * 密码验证工具
 * 用于验证数据库中BCrypt哈希值对应的原始密码
 */
public class PasswordChecker {
    
    public static void main(String[] args) {
        // 数据库中的BCrypt哈希值
        String storedHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou";
        
        // 测试可能的密码
        String[] possiblePasswords = {
            "admin123",
            "admin",
            "123456",
            "password",
            "freebone",
            "FREEBONE",
            "bonesys",
            "BONESYS"
        };
        
        System.out.println("🔐 验证数据库中的BCrypt哈希值对应的原始密码");
        System.out.println("哈希值: " + storedHash);
        System.out.println();
        
        for (String password : possiblePasswords) {
            boolean matches = BCrypt.checkpw(password, storedHash);
            System.out.println("密码 '" + password + "': " + (matches ? "✅ 匹配" : "❌ 不匹配"));
            
            if (matches) {
                System.out.println();
                System.out.println("🎉 找到正确密码: " + password);
                System.out.println("📝 登录信息:");
                System.out.println("   用户名: admin");
                System.out.println("   密码: " + password);
                return;
            }
        }
        
        System.out.println();
        System.out.println("❌ 未找到匹配的密码，可能需要尝试其他密码");
        
        // 生成新的BCrypt哈希值示例
        System.out.println();
        System.out.println("🔧 生成新的BCrypt哈希值示例:");
        String newPassword = "admin123";
        String newHash = BCrypt.hashpw(newPassword, BCrypt.gensalt());
        System.out.println("密码 '" + newPassword + "' 的BCrypt哈希: " + newHash);
        System.out.println("验证: " + (BCrypt.checkpw(newPassword, newHash) ? "✅ 通过" : "❌ 失败"));
    }
}
