package com.Bone.BoneSys.dto.hardware;

import com.Bone.BoneSys.dto.PaginationInfo;
import java.util.List;

/**
 * 治疗头管理页面响应DTO
 * 包含治疗头列表和分页信息
 */
public class TreatmentHeadManagementResponse {
    
    private List<TreatmentHeadDetailInfo> heads;
    private PaginationInfo pagination;
    
    public TreatmentHeadManagementResponse() {}
    
    public TreatmentHeadManagementResponse(List<TreatmentHeadDetailInfo> heads, PaginationInfo pagination) {
        this.heads = heads;
        this.pagination = pagination;
    }
    
    public List<TreatmentHeadDetailInfo> getHeads() {
        return heads;
    }
    
    public void setHeads(List<TreatmentHeadDetailInfo> heads) {
        this.heads = heads;
    }
    
    public PaginationInfo getPagination() {
        return pagination;
    }
    
    public void setPagination(PaginationInfo pagination) {
        this.pagination = pagination;
    }
    
    /**
     * 治疗头详细信息（用于管理页面）
     */
    public static class TreatmentHeadDetailInfo {
        private int headNumber;
        private String textStatus;      // 文本状态（如"充电中"）
        private String iconStatus;      // 图标状态（如"CHARGED"）
        private int usageCount;
        private String totalUsageTime;  // 总使用时间（格式化字符串）
        private int batteryLevel;
        private String lightColor;      // 指示灯颜色
        
        public TreatmentHeadDetailInfo() {}
        
        public TreatmentHeadDetailInfo(int headNumber, String textStatus, String iconStatus, 
                                     int usageCount, String totalUsageTime, int batteryLevel, String lightColor) {
            this.headNumber = headNumber;
            this.textStatus = textStatus;
            this.iconStatus = iconStatus;
            this.usageCount = usageCount;
            this.totalUsageTime = totalUsageTime;
            this.batteryLevel = batteryLevel;
            this.lightColor = lightColor;
        }
        
        // Getters and Setters
        public int getHeadNumber() { return headNumber; }
        public void setHeadNumber(int headNumber) { this.headNumber = headNumber; }
        
        public String getTextStatus() { return textStatus; }
        public void setTextStatus(String textStatus) { this.textStatus = textStatus; }
        
        public String getIconStatus() { return iconStatus; }
        public void setIconStatus(String iconStatus) { this.iconStatus = iconStatus; }
        
        public int getUsageCount() { return usageCount; }
        public void setUsageCount(int usageCount) { this.usageCount = usageCount; }
        
        public String getTotalUsageTime() { return totalUsageTime; }
        public void setTotalUsageTime(String totalUsageTime) { this.totalUsageTime = totalUsageTime; }
        
        public int getBatteryLevel() { return batteryLevel; }
        public void setBatteryLevel(int batteryLevel) { this.batteryLevel = batteryLevel; }
        
        public String getLightColor() { return lightColor; }
        public void setLightColor(String lightColor) { this.lightColor = lightColor; }
    }
}