-- ====================================================================================
-- 密码简化更新脚本
-- 将BCrypt哈希密码改为明文密码，适用于离线医疗设备
-- ====================================================================================

USE `bonesys`;

-- 更新用户密码为明文
UPDATE `users` SET 
    `factory_password_hash` = 'factory123',
    `user_password_hash` = 'admin123',
    `last_updated_at` = NOW()
WHERE `id` = 1;

-- 验证更新结果
SELECT 
    id,
    username,
    factory_password_hash as '厂家密码',
    user_password_hash as '用户密码',
    last_updated_at as '更新时间'
FROM `users` 
WHERE `id` = 1;

-- 显示更新完成信息
SELECT '密码已更新为明文格式，现在可以使用以下密码登录：' as '更新完成';
SELECT 'admin123' as '用户登录密码', 'factory123' as '厂家重置密码';