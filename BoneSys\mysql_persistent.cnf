# MySQL持久化配置文件
# 解决数据库重启后数据消失的问题

[mysqld]
# 基本设置
port = 3306
bind-address = 127.0.0.1

# 数据目录（确保这个目录存在且有写权限）
datadir = /var/lib/mysql

# 字符集设置
character-set-server = utf8mb4
collation-server = utf8mb4_unicode_ci

# InnoDB设置（确保数据持久化）
innodb_flush_log_at_trx_commit = 1
innodb_doublewrite = 1
innodb_file_per_table = 1
innodb_buffer_pool_size = 128M

# 二进制日志（确保数据安全）
log-bin = mysql-bin
sync_binlog = 1
expire_logs_days = 7

# 错误日志
log-error = /var/log/mysql/error.log

# 慢查询日志
slow_query_log = 1
slow_query_log_file = /var/log/mysql/slow.log
long_query_time = 2

# 连接设置
max_connections = 100
max_connect_errors = 10

[mysql]
default-character-set = utf8mb4

[client]
default-character-set = utf8mb4