@echo off
chcp 65001 >nul
echo ====================================
echo 治疗头管理接口测试
echo ====================================
echo.

echo 正在测试 GET /api/hardware/heads 接口...
echo.

echo 1. 测试基本请求（默认分页）:
curl -X GET "http://localhost:8080/api/hardware/heads" ^
  -H "Content-Type: application/json" ^
  -w "\n状态码: %%{http_code}\n" ^
  -s

echo.
echo ====================================
echo.

echo 2. 测试分页请求:
curl -X GET "http://localhost:8080/api/hardware/heads?page=1&size=5" ^
  -H "Content-Type: application/json" ^
  -w "\n状态码: %%{http_code}\n" ^
  -s

echo.
echo ====================================
echo.

echo 3. 测试第二页:
curl -X GET "http://localhost:8080/api/hardware/heads?page=2&size=10" ^
  -H "Content-Type: application/json" ^
  -w "\n状态码: %%{http_code}\n" ^
  -s

echo.
echo ====================================
echo 测试完成
echo ====================================
pause