<template>
  <div class="page flex-col">
    <div class="group_1 flex-col">
      <div class="box_1 flex-col">
        <div class="block_1 flex-row justify-between">
          <img
            class="image_1"
            referrerpolicy="no-referrer"
            src="../assets/images/personalinformation/dc89535b738c9579089ef14b8aa7dc2d.png"
            @click="goBack"
          />
          <span class="text_1">个人信息</span>
        </div>
        <div class="block_2 flex-row justify-between">
          <div class="box_2 flex-row">
            <div class="block_3 flex-col">
              <!-- 头像占位 -->
            </div>
            <div class="block_4 flex-col">
              <div class="box_4 flex-col"></div>
              <span class="text_3">姓名：{{ patientData.name || '' }}</span>
              <span class="text_4">性别：{{ patientData.sex === 1 ? '男' : patientData.sex === 0 ? '女' : '' }}</span>
              <span class="text_6">年龄：{{ patientData.age || '' }}</span>

              <div class="text-wrapper_1 flex-row">
                <span class="text_5">编号：{{ patientData.id || '' }}</span>
                <span class="text_7">电话：{{ patientData.phone || '' }}</span>
              </div>
              <div class="text-wrapper_2 flex-row justify-between">
                <span class="text_8">就诊卡号：{{ patientData.medicalRecordId || '' }}</span>
              </div>
            </div>
            <div class="block_5 flex-col">
              <div class="section_1 flex-col">
                <div class="text-wrapper_4 flex-col" @click="showDiagnosisDialog"><span class="text_14">诊断详情</span></div>
              </div>
            </div>
          </div>
          <div class="box_5 flex-col">
            <div class="text-wrapper_5 flex-row">
              <span class="text_15">肩颈部：</span>
              <span class="text_16">{{ treatmentSummary.shoulderNeck || '0' }}min</span>
              <span class="text_17">上肢：</span>
              <span class="text_18">{{ treatmentSummary.upperLimb || '0' }}min</span>
            </div>
            <div class="text-wrapper_6 flex-row">
              <span class="text_19">腰背部：</span>
              <span class="text_20">{{ treatmentSummary.lumbarBack || '0' }}min</span>
              <span class="text_21">下肢：</span>
              <span class="text_22">{{ treatmentSummary.lowerLimb || '0' }}min</span>
            </div>
            <div class="text-wrapper_7 flex-row">
              <span class="text_23">其他部位：</span>
              <span class="text_24">{{ treatmentSummary.other || '0' }}min</span>
              <span class="text_25">髋部：</span>
              <span class="text_26">{{ treatmentSummary.hip || '0' }}min</span>
            </div>
            <div class="group_2 flex-row">
              <div class="text-wrapper_8 flex-col"><span class="text_27">治疗时间合计 </span></div>
            </div>
          </div>
        </div>
        <div class="block_6 flex-col">
          <div class="text-wrapper_9 flex-row">
            <span class="text_28">就诊卡号</span>
            <span class="text_29">姓名</span>
            <span class="text_30">日期</span>
            <span class="text_31">治疗部位</span>
            <span class="text_32">有效声强</span>
            <span class="text_33">治疗头编号</span>
            <span class="text_34">治疗时长</span>
          </div>
          <img
            class="image_2"
            referrerpolicy="no-referrer"
            src="../assets/images/personalinformation/f6c0e0d2b0627d99de5a294d543c2857.png"
          />
          
          <!-- 治疗记录列表 -->
          <template v-if="paginatedTreatmentRecords.length > 0">
            <div v-for="(record, index) in paginatedTreatmentRecords" :key="index">
              <div class="block_7 flex-row" v-if="index % 2 === 0">
                <span class="text_35">{{ record.medicalRecordId || '' }}</span>
                <span class="text_36">{{ record.patientName || '' }}</span>
                <span class="text_37">{{ formatDate(record.treatmentDate) || '' }}</span>
                <span class="text_38">{{ getTreatmentAreaName(record.treatmentArea) || '' }}</span>
                <div class="group_3 flex-col">
                  <div class="box_6 flex-col"></div>
                </div>
                <span class="text_39">{{ record.effectiveIntensity || '' }}</span>
                <span class="text_40">{{ record.treatmentHeadNumber || '' }}</span>
                <span class="text_41">{{ record.treatmentDuration || '' }}min</span>
              </div>
              <div class="block_8 flex-row" v-else>
                <span class="text_42">{{ record.medicalRecordId || '' }}</span>
                <span class="text_43">{{ record.patientName || '' }}</span>
                <span class="text_44">{{ formatDate(record.treatmentDate) || '' }}</span>
                <span class="text_45">{{ getTreatmentAreaName(record.treatmentArea) || '' }}</span>
                <div class="section_2 flex-col">
                  <span class="text_46">{{ record.effectiveIntensity || '' }}</span>
                  <div class="box_7 flex-col">
                    <div class="text-wrapper_10">
                      <span class="text_47">mW/cm</span>
                      <span class="text_48">{{ record.treatmentHeadNumber || '' }}</span>
                    </div>
                  </div>
                </div>
                <span class="text_49">{{ record.treatmentHeadNumber || '' }}</span>
                <span class="text_50">{{ record.treatmentDuration || '' }}min</span>
              </div>
              <img
                class="image_3"
                referrerpolicy="no-referrer"
                :src="index % 2 === 0 
                  ? '../assets/images/personalinformation/6a6652c6bb73775047f4c74738e4cdaf.png'
                  : '../assets/images/personalinformation/b6bbf3f36b270fd2a63b12c0d6c57544.png'"
              />
            </div>
          </template>
          <div v-else class="no-data">
            暂无治疗记录
          </div>
          
          <!-- 分页控件 -->
          <div class="pagination-container" v-if="totalTreatmentPages > 1">
            <div class="pagination-controls">
              <div class="pagination-btn prev-btn"
                  @click="prevTreatmentPage"
                  :class="{ disabled: currentTreatmentPage === 1 }">
                <img src="../assets/images/patientmangement/pscfuqtlgklge35gqvwmjemf1ftzt5cw0k164a5578-5e3c-4723-bc66-875bb1108d45.png" />
              </div>
              <div class="page-info">
                第 {{ currentTreatmentPage }} 页/共 {{ totalTreatmentPages }} 页
              </div>
              <div class="pagination-btn next-btn"
                  @click="nextTreatmentPage"
                  :class="{ disabled: currentTreatmentPage === totalTreatmentPages }">
                <img src="../assets/images/patientmangement/psmqhsvt3d8c3z2wc722ghc6ga6sph03fs58fbf288-2ea0-4006-995a-1fb501817a56.png" />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <!-- 诊断详情弹窗 -->
    <div v-if="showDiagnosisDialogFlag" class="diagnosis-dialog-overlay" @click.self="closeDiagnosisDialog">
      <div class="diagnosis-box flex-col">
        <div class="diagnosis-content-box flex-col">
          <div class="diagnosis-header">
            <span class="diagnosis-title">诊断详情</span>
            <img
              class="diagnosis-close"
              referrerpolicy="no-referrer"
              src="../assets/images/personalinformation/a9c5385f3faed3fd6264136b3ea77b1e.png"
              @click="closeDiagnosisDialog"
            />
          </div>
          <div class="diagnosis-body flex-col">
            <textarea 
              class="diagnosis-textarea" 
              v-model="editableDiagnosis"
              placeholder="请输入诊断信息"
            ></textarea>
          </div>
          <div class="diagnosis-footer flex-col" @click="saveDiagnosis">
            <div class="diagnosis-confirm flex-col"><span class="diagnosis-confirm-text">确认修改</span></div>
          </div>
        </div>
      </div>
    </div>
    </div>

</template>

<script>
import { ref, onMounted, computed } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import http from '@/utils/axios';
import { MessagePlugin } from 'tdesign-vue-next';

export default {
  name: 'PatientDetailView',
  setup() {
    const route = useRoute();
    const router = useRouter();
    
    // 患者数据
    const patientData = ref({
      id: '',
      name: '',
      sex: null,
      age: '',
      phone: '',
      medicalRecordId: '',
      diagnosis: '',
      creationTimestamp: ''
    });

    // 治疗记录数据
    const treatmentRecords = ref([]);

    // 治疗记录分页
    const currentTreatmentPage = ref(1);
    const treatmentPageSize = ref(3);

    // 诊断详情弹窗
    const showDiagnosisDialogFlag = ref(false);
    
    // 可编辑的诊断信息
    const editableDiagnosis = ref('');

    // 治疗汇总数据
    const treatmentSummary = ref({
      shoulderNeck: 0,
      upperLimb: 0,
      lumbarBack: 0,
      lowerLimb: 0,
      hip: 0,
      other: 0,
      total: 0
    });

    // 计算当前页的治疗记录
    const paginatedTreatmentRecords = computed(() => {
      const startIndex = (currentTreatmentPage.value - 1) * treatmentPageSize.value;
      return treatmentRecords.value.slice(startIndex, startIndex + treatmentPageSize.value);
    });

    // 计算治疗记录总页数
    const totalTreatmentPages = computed(() => {
      return Math.ceil(treatmentRecords.value.length / treatmentPageSize.value);
    });

    // 根据就诊卡号获取患者详细信息
    const fetchPatientDetailByMedicalRecordId = async (medicalRecordId) => {
      try {
        // 获取患者基本信息
        const searchResponse = await http.get(`/api/patients?medicalRecordId=${medicalRecordId}`);
        if (searchResponse.data && searchResponse.data.length > 0) {
          patientData.value = searchResponse.data[0];
          
          // 获取治疗记录
          await fetchTreatmentRecords(medicalRecordId);
          
          // 计算治疗汇总
          calculateTreatmentSummary();
        } else {
          MessagePlugin.error('未找到该患者信息');
        }
      } catch (error) {
        console.error('获取患者详情失败', error);
        MessagePlugin.error('获取患者详情失败，请稍后重试');
      }
    };

    // 获取治疗记录
    const fetchTreatmentRecords = async (medicalRecordId) => {
      try {
        // 假设有一个获取治疗记录的API
        const response = await http.get(`/api/treatments?medicalRecordId=${medicalRecordId}`);
        if (response.data) {
          treatmentRecords.value = response.data;
        }
      } catch (error) {
        console.error('获取治疗记录失败', error);
        // 即使治疗记录获取失败，也不影响患者基本信息显示
      }
    };

    // 计算治疗汇总数据
    const calculateTreatmentSummary = () => {
      // 重置统计数据
      treatmentSummary.value = {
        shoulderNeck: 0,
        upperLimb: 0,
        lumbarBack: 0,
        lowerLimb: 0,
        hip: 0,
        other: 0,
        total: 0
      };

      // 遍历治疗记录进行统计
      treatmentRecords.value.forEach(record => {
        const duration = parseInt(record.treatmentDuration) || 0;
        treatmentSummary.value.total += duration;
        
        // 根据治疗部位分类统计（需要根据实际的治疗部位字段进行调整）
        switch(record.treatmentArea) {
          case 1: // 肩颈部
            treatmentSummary.value.shoulderNeck += duration;
            break;
          case 2: // 上肢
            treatmentSummary.value.upperLimb += duration;
            break;
          case 3: // 腰背部
            treatmentSummary.value.lumbarBack += duration;
            break;
          case 4: // 下肢
            treatmentSummary.value.lowerLimb += duration;
            break;
          case 5: // 髋部
            treatmentSummary.value.hip += duration;
            break;
          default: // 其他部位
            treatmentSummary.value.other += duration;
        }
      });
    };

    // 获取治疗部位名称
    const getTreatmentAreaName = (areaCode) => {
      const areaMap = {
        1: '肩颈部',
        2: '上肢',
        3: '腰背部',
        4: '下肢',
        5: '髋部'
      };
      return areaMap[areaCode] || '其他部位';
    };

    // 格式化日期
    const formatDate = (dateString) => {
      if (!dateString) return '';
      const date = new Date(dateString);
      return `${date.getFullYear()}.${date.getMonth() + 1}.${date.getDate()}`;
    };

    // 返回上一页
    const goBack = () => {
      router.go(-1);
    };

    // 显示诊断详情弹窗
    const showDiagnosisDialog = () => {
      editableDiagnosis.value = patientData.value.diagnosis || '';
      showDiagnosisDialogFlag.value = true;
    };

    // 关闭诊断详情弹窗
    const closeDiagnosisDialog = () => {
      showDiagnosisDialogFlag.value = false;
    };

    // 保存诊断信息
    const saveDiagnosis = async () => {
      try {
        // 调用API更新诊断信息
        const response = await http.put(`/api/patients/${patientData.value.id}`, {
          ...patientData.value,
          diagnosis: editableDiagnosis.value
        });
        
        if (response.status === 200) {
          // 更新本地数据
          patientData.value.diagnosis = editableDiagnosis.value;
          // 关闭弹窗
          closeDiagnosisDialog();
          MessagePlugin.success('诊断信息保存成功');
        } else {
          MessagePlugin.error('保存失败，请重试');
        }
      } catch (error) {
        console.error('保存诊断信息失败', error);
        MessagePlugin.error('保存诊断信息失败，请稍后重试');
      }
    };

    // 治疗记录分页 - 上一页
    const prevTreatmentPage = () => {
      if (currentTreatmentPage.value > 1) {
        currentTreatmentPage.value--;
      }
    };

    // 治疗记录分页 - 下一页
    const nextTreatmentPage = () => {
      if (currentTreatmentPage.value < totalTreatmentPages.value) {
        currentTreatmentPage.value++;
      }
    };

    // 组件挂载时获取数据
    onMounted(() => {
      // 从路由参数中获取就诊卡号
      const medicalRecordId = route.params.medicalRecordId || route.query.medicalRecordId;
      if (medicalRecordId) {
        fetchPatientDetailByMedicalRecordId(medicalRecordId);
      } else {
        MessagePlugin.warning('未找到患者就诊卡号');
      }
    });

    return {
      patientData,
      treatmentRecords,
      treatmentSummary,
      showDiagnosisDialogFlag,
      editableDiagnosis,
      currentTreatmentPage,
      treatmentPageSize,
      totalTreatmentPages,
      paginatedTreatmentRecords,
      goBack,
      formatDate,
      getTreatmentAreaName,
      showDiagnosisDialog,
      closeDiagnosisDialog,
      saveDiagnosis,
      prevTreatmentPage,
      nextTreatmentPage
    };
  }
};
</script>
<style scoped lang="css">
/* 保留原有样式不变 */
.page {
  background-color: rgba(255, 255, 255, 1);
  position: relative;
  width: 1920px;
  height: 1080px;
  overflow: hidden;
}

.group_1 {
  height: 1078px;
  background: url(../assets/images/personalinformation/4da74a82a76211bac6221a1e0edabddc.png) -3px -2px
    no-repeat;
  background-size: 1920px 1080px;
  width: 1917px;
  position: relative;
}

.box_1 {
  position: absolute;
  width: 1920px;
  height: 1080px;
  background: url(../assets/images/personalinformation/c30b12fbf3037e890c790dcd36bdf001.png) -3px -2px
    no-repeat;
  background-size: 1923px 1082px;
}

.block_1 {
  width: 1004px;
  height: 70px;
  margin: 18px 0 0 100px;
}

.image_1 {
  width: 169px;
  height: 70px;
  cursor: pointer;
}

.text_1 {
  width: 294px;
  height: 49px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: normal;
  text-align: center;
  white-space: nowrap;
  line-height: 49px;
  margin: 0 auto;
  position: absolute;
  top: 20px;
  left: 0;
  right: 0;
}

.block_2 {
  width: 1627px;
  height: 275px;
  margin: 70px 0 0 136px;
  display: flex;
  justify-content: space-between;
}

.box_2 {
  width: 941px;
  height: 273px;
  background: url(../assets/images/personalinformation/e974f01faef346da53cab0c398e50bea.png)
    0px -1px no-repeat;
  background-size: 942px 274px;
  position: relative;
}

.block_3 {
  background-color: rgba(239, 239, 239, 1);
  border-radius: 50%;
  width: 155px;
  height: 155px;
  border: 1px solid rgba(203, 203, 203, 1);
  margin: 20px 0 0 0px;
  position: absolute;
  top: 30px; /* 调整垂直位置 */
  left: 30px;
}

.block_4 {
  position: absolute;
  width: 321px;
  height: 156px;
  margin:43px 0 0 100px;
  top: 0px; /* 调整垂直位置 */
  left: 120px;
}

.box_4 {
  position: relative;
  border-radius: 20px;
  width: 80px;
  height: 49px;
  margin-left: 1px;
  top: 0px; /* 调整垂直位置 */
  left: 120px;
}

.text_3 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 14px;
  position: absolute;
  top: 0;
  left: 0;
}

.text_4 {
  width: 68px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin: 14px 0 0 102px;
  position: absolute;
  top: 0;
  left: 102px;
}

.text_6 {
  width: 68px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  /* 移除 margin: 0px 0 0 500px; */
  position: absolute;
  top: 14px;
  left: 400px; /* 调整此值以控制 text_6 相对于父元素的位置 */
}

.text-wrapper_1 {
  width: 317px;
  height: 25px;
  margin-top: 21px;
}

.text_5 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
}

.text_7 {
  width: 63px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
  margin-left: 128px;
}

.text-wrapper_2 {
  width: 257px;
  height: 25px;
  margin-top: 36px;
}

.text_8 {
  width: 127px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 16px;
}

.block_5 {
  width: 368px;
  height: 220px;
  margin: 37px 27px 0 20px;
}

.section_1 {
  height: 69px;
  background: url(../assets/images/personalinformation/8868f2acf37f3079fab139b3ff5da01f.png) -10px
    0px no-repeat;
  background-size: 252px 92px;
  width: 232px;
  margin: 20px 0 0 650px;
  position: relative;
  top: 150px;
  left: 0px;
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.text-wrapper_4 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  /* 移除 margin 设置 */
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.text_14 {
  width: 175px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin-top: 20px;
  margin-left: 5px;
  letter-spacing: 10px;
  cursor: pointer; /* 添加指针样式 */
}

.box_5 {
  height: 275px;
  background: url(../assets/images/personalinformation/e19c135acf78908ffc6870560b6ac03d.png)
    100% no-repeat;
  background-size: 100% 100%;
  width: 636px;
  align-self: flex-start;
}

.text-wrapper_5 {
  position: relative;
  width: 468px;
  height: 26px;
  margin: -15px 0 0 68px;
  top: 0px; /* 调整垂直位置 */
  left: 0px;
}

.text_15 {
  width: 92px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 1px;
}

.text_16 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 2px 0 0 17px;
}

.text_17 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 76px;
  position: absolute;
  left:200px;
}

.text_18 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 2px 0 0 19px;
  position: absolute;
  left: 325px;
}

.text-wrapper_6 {
  width: 468px;
  height: 25px;
  margin: 35px 0 0 68px;
}

.text_19 {
  width: 92px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
}

.text_20 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 17px;
}

.text_21 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 76px;
  position: absolute;
  right:385px;
}

.text_22 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 19px;
  position: absolute;
  right: 280px;
}

.text-wrapper_7 {
  width: 468px;
  height: 29px;
  margin: 35px 0 0 68px;
}

.text_23 {
  width: 120px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-top: 4px;
}

.text_24 {
  width: 83px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 7px 0 0 23px;
}

.text_25 {
  width: 64px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin-left: 60px;
  position: absolute;
  right:385px;
}

.text_26 {
  width: 100px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(60, 60, 60, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 155px;
  margin: 1px 0 0 18px;
  position: absolute;
  right: 280px;
}

.group_2 {
  width: 232px;
  height: 46px;
  margin: 80px 0 20px 377px;
}

.text-wrapper_8 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 20px;
  height: 50px;
  border: 2px solid rgba(245, 245, 245, 0.1);
  width: 236px;
  margin: -2px 0 0 -2px;
  display: flex; /* 添加弹性布局 */
  justify-content: center; /* 水平居中 */
  align-items: center; /* 垂直居中 */
}

.text_27 {
  width: 195px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center; /* 文字居中 */
  white-space: nowrap;
  line-height: 16px;
  /* 移除 margin 设置，因为父元素已经居中 */
}

.block_6 {
  position: relative;
  width: 1689px;
  height: 574px;
  background: url(../assets/images/personalinformation/89849cff18b90a27d9b139bda364e8ce.png)
    100% no-repeat;
  background-size: 100% 100%;
  margin: 30px 0 43px 109px;
}

.text-wrapper_9 {
  width: 1444px;
  height: 33px;
  margin: 73px 0 0 137px;
  padding-top: 60px; /* 添加上内边距 */
}

.text_28 {
  width: 143px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
}

.text_29 {
  width: 66px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 122px;
}

.text_30 {
  width: 63px;
  height: 32px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 1px 0 0 118px;
}

.text_31 {
  width: 143px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 93px;
}

.text_32 {
  width: 143px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 89px;
}

.text_33 {
  width: 179px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 66px;
}

.text_34 {
  width: 142px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 33px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-left: 77px;
}

.image_2 {
  width: 1476px;
  height: 1px;
  margin: 60px 0 0 113px;
}

.block_7 {
  width: 1384px;
  height: 41px;
  margin: 16px 0 0 165px;
}

.text_35 {
  width: 90px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 12px;
}

.text_36 {
  width: 107px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 128px;
}

.text_37 {
  width: 105px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 73px;
}

.text_38 {
  width: 80px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 105px;
}

.group_3 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 96px;
  width: 168px;
}

.box_6 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  width: 117px;
  height: 41px;
  border: 0.5px solid rgba(191, 191, 191, 1);
  margin-left: 67px;
}

.text_39 {
  width: 23px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 143px;
}

.text_40 {
  width: 23px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 188px;
}

.text_41 {
  width: 78px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 12px 0 0 188px;
}

.image_3 {
  width: 1476px;
  height: 1px;
  margin: 13px 0 0 113px;
}

.block_8 {
  width: 1384px;
  height: 41px;
  margin: 14px 0 0 165px;
}

.text_42 {
  width: 90px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 11px;
}

.text_43 {
  width: 107px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 128px;
}

.text_44 {
  width: 105px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 73px;
}

.text_45 {
  width: 80px;
  height: 25px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 10px 0 0 105px;
}

.section_2 {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(159, 159, 160, 1);
  margin-left: 95px;
  width: 168px;
  position: relative;
}

.text_46 {
  width: 42px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 127px;
  margin: 9px 0 0 14px;
}

.box_7 {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 1px;
  height: 41px;
  border: 0.5px solid rgba(191, 191, 191, 1);
  width: 117px;
  position: absolute;
  left: 67px;
  top: 0;
}

.text-wrapper_10 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  font-size: 0;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
  margin: 8px 0 0 8px;
}

.text_47 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
}

.text_48 {
  width: 102px;
  height: 23px;
  overflow-wrap: break-word;
  color: rgba(138, 137, 138, 1);
  font-size: 25px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: left;
  white-space: nowrap;
  line-height: 102px;
}

.text_49 {
  width: 23px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 144px;
}

.text_50 {
  width: 78px;
  height: 21px;
  overflow-wrap: break-word;
  color: rgba(0, 0, 0, 1);
  font-size: 25px;
  font-family: MicrosoftYaHeiLight;
  font-weight: 300;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin: 11px 0 0 188px;
}

/* 修改诊断详情弹窗样式 */
.diagnosis-dialog-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.diagnosis-box {
  height: 1080px;
  background: url(../assets/images/personalinformation/8011ab97892b73a8ac95abaff0b3a6f1.png) -3px -2px
    no-repeat;
  background-size: 1952px 1094px;
  width: 1920px;
  position: absolute;
}

.diagnosis-content-box {
  background-color: rgba(231, 241, 240, 1);
  border-radius: 26px;
  width: 1219px;
  height: 893px;
  border: 4px solid rgba(62, 160, 149, 1);
  margin: 108px 0 0 362px;
}

.diagnosis-header {
  width: 670px;
  height: 73px;
  margin: 41px 0 0 463px;
  position: relative;
  display: block;
}

.diagnosis-title {
  width: 294px;
  height: 50px;
  overflow-wrap: break-word;
  color: rgba(1, 1, 1, 1);
  font-size: 50px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 41px;
  margin-top: 16px;
  position: absolute;
  left: 135px;
  top: 0;
  transform: translateX(-50%);
}

.diagnosis-close {
  width: 75px;
  height: 75px;
  cursor: pointer;
  position: absolute;
  top: 25px;
  right: 0;
}

.diagnosis-body {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 26px;
  width: 925px;
  height: 508px;
  border: 4px solid rgba(184, 254, 246, 1);
  margin: 67px 0 0 148px;
  overflow-y: auto;
}

.diagnosis-textarea {
  width: 100%;
  height: 100%;
  border: none;
  outline: none;
  resize: none;
  font-size: 28px;
  color: #333;
  line-height: 1.6;
  padding: 30px;
  box-sizing: border-box;
  background-color: transparent;
}

.diagnosis-textarea::placeholder {
  color: #999;
}

.diagnosis-footer {
  height: 69px;
  background: url(../assets/images/personalinformation/ab196a073e26a39b689f8b4623da4884.png) -10px
    0px no-repeat;
  background-size: 253px 92px;
  width: 232px;
  margin: 55px 0 80px 490px;
  cursor: pointer;
  display: flex;
  justify-content: center;
  align-items: center;
}

.diagnosis-confirm {
  background-color: rgba(255, 255, 255, 1);
  border-radius: 22px;
  height: 64px;
  width: 226px;
  margin: 1px 0 0 1px;
  display: flex;
  justify-content: center;
  align-items: center;
}

.diagnosis-confirm-text {
  width: 153px;
  height: 33px;
  overflow-wrap: break-word;
  color: rgba(78, 78, 78, 1);
  font-size: 33px;
  font-family: MicrosoftYaHei;
  font-weight: NaN;
  text-align: center;
  white-space: nowrap;
  line-height: 16px;
  margin-top:10px;
}



/* 治疗记录分页样式 */
.pagination-container {
  position: absolute;
  right: 60px;
  bottom: 50px;
}

.pagination-controls {
  display: flex;
  align-items: center;
  background: rgba(255, 255, 255, 0.8);
  padding: 5px 15px;
  border-radius: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.page-info {
  margin: 0 15px;
  font-family: 'PingFang SC', 'Microsoft YaHei', sans-serif;
  font-size: 16px;
  color: #333;
}

.pagination-btn {
  width: 40px;
  height: 40px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.pagination-btn:hover:not(.disabled) {
  transform: scale(1.05);
}

.pagination-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.no-data {
  text-align: center;
  font-size: 25px;
  color: #666;
  margin-top: 50px;
}
</style>