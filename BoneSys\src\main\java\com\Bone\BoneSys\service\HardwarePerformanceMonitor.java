package com.Bone.BoneSys.service;

import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import jakarta.annotation.PostConstruct;
import jakarta.annotation.PreDestroy;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.Executors;
import java.util.concurrent.ScheduledExecutorService;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicLong;
import java.util.concurrent.atomic.LongAdder;

/**
 * 硬件性能监控服务
 * 收集和分析硬件通信性能指标
 */
@Service
public class HardwarePerformanceMonitor {
    
    private static final Logger logger = LoggerFactory.getLogger(HardwarePerformanceMonitor.class);
    
    // 性能指标
    private final LongAdder totalCommands = new LongAdder();
    private final LongAdder successfulCommands = new LongAdder();
    private final LongAdder failedCommands = new LongAdder();
    private final LongAdder timeoutCommands = new LongAdder();
    private final AtomicLong totalResponseTime = new AtomicLong(0);
    private final AtomicLong maxResponseTime = new AtomicLong(0);
    private final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
    
    // 按命令类型分类的统计
    private final ConcurrentHashMap<String, CommandStats> commandTypeStats = new ConcurrentHashMap<>();
    
    // 最近的响应时间（用于计算移动平均）
    private final ConcurrentHashMap<Long, Long> recentResponseTimes = new ConcurrentHashMap<>();
    
    private ScheduledExecutorService metricsReportExecutor;
    
    @PostConstruct
    public void initialize() {
        metricsReportExecutor = Executors.newSingleThreadScheduledExecutor(r -> {
            Thread t = new Thread(r, "HardwareMetricsReport");
            t.setDaemon(true);
            return t;
        });
        
        // 每分钟输出性能报告
        metricsReportExecutor.scheduleWithFixedDelay(this::reportMetrics, 60, 60, TimeUnit.SECONDS);
        
        // 每10秒清理旧的响应时间记录
        metricsReportExecutor.scheduleWithFixedDelay(this::cleanupOldMetrics, 10, 10, TimeUnit.SECONDS);
        
        logger.info("Hardware performance monitor initialized");
    }
    
    @PreDestroy
    public void cleanup() {
        if (metricsReportExecutor != null && !metricsReportExecutor.isShutdown()) {
            metricsReportExecutor.shutdown();
            try {
                if (!metricsReportExecutor.awaitTermination(5, TimeUnit.SECONDS)) {
                    metricsReportExecutor.shutdownNow();
                }
            } catch (InterruptedException e) {
                metricsReportExecutor.shutdownNow();
                Thread.currentThread().interrupt();
            }
        }
        logger.info("Hardware performance monitor cleaned up");
    }
    
    /**
     * 记录命令开始执行
     */
    public long recordCommandStart(String commandType) {
        totalCommands.increment();
        
        // 获取或创建命令类型统计
        commandTypeStats.computeIfAbsent(commandType, k -> new CommandStats()).totalCount.increment();
        
        return System.currentTimeMillis();
    }
    
    /**
     * 记录命令执行成功
     */
    public void recordCommandSuccess(String commandType, long startTime) {
        long responseTime = System.currentTimeMillis() - startTime;
        
        successfulCommands.increment();
        totalResponseTime.addAndGet(responseTime);
        
        // 更新最大最小响应时间
        updateMinMaxResponseTime(responseTime);
        
        // 记录最近的响应时间
        recentResponseTimes.put(System.currentTimeMillis(), responseTime);
        
        // 更新命令类型统计
        CommandStats stats = commandTypeStats.get(commandType);
        if (stats != null) {
            stats.successCount.increment();
            stats.totalResponseTime.addAndGet(responseTime);
            stats.updateMinMaxResponseTime(responseTime);
        }
        
        logger.debug("Command {} completed successfully in {}ms", commandType, responseTime);
    }
    
    /**
     * 记录命令执行失败
     */
    public void recordCommandFailure(String commandType, long startTime, String errorType) {
        long responseTime = System.currentTimeMillis() - startTime;
        
        failedCommands.increment();
        
        if ("TIMEOUT".equals(errorType)) {
            timeoutCommands.increment();
        }
        
        // 更新命令类型统计
        CommandStats stats = commandTypeStats.get(commandType);
        if (stats != null) {
            stats.failureCount.increment();
            if ("TIMEOUT".equals(errorType)) {
                stats.timeoutCount.increment();
            }
        }
        
        logger.warn("Command {} failed after {}ms, error: {}", commandType, responseTime, errorType);
    }
    
    /**
     * 获取性能统计信息
     */
    public PerformanceMetrics getMetrics() {
        PerformanceMetrics metrics = new PerformanceMetrics();
        
        metrics.totalCommands = totalCommands.sum();
        metrics.successfulCommands = successfulCommands.sum();
        metrics.failedCommands = failedCommands.sum();
        metrics.timeoutCommands = timeoutCommands.sum();
        
        if (metrics.totalCommands > 0) {
            metrics.successRate = (double) metrics.successfulCommands / metrics.totalCommands * 100;
            metrics.failureRate = (double) metrics.failedCommands / metrics.totalCommands * 100;
            metrics.timeoutRate = (double) metrics.timeoutCommands / metrics.totalCommands * 100;
        }
        
        if (metrics.successfulCommands > 0) {
            metrics.averageResponseTime = (double) totalResponseTime.get() / metrics.successfulCommands;
        }
        
        metrics.maxResponseTime = maxResponseTime.get() == 0 ? 0 : maxResponseTime.get();
        metrics.minResponseTime = minResponseTime.get() == Long.MAX_VALUE ? 0 : minResponseTime.get();
        
        // 计算最近1分钟的平均响应时间
        metrics.recentAverageResponseTime = calculateRecentAverageResponseTime();
        
        // 复制命令类型统计
        commandTypeStats.forEach((type, stats) -> {
            CommandTypeMetrics typeMetrics = new CommandTypeMetrics();
            typeMetrics.commandType = type;
            typeMetrics.totalCount = stats.totalCount.sum();
            typeMetrics.successCount = stats.successCount.sum();
            typeMetrics.failureCount = stats.failureCount.sum();
            typeMetrics.timeoutCount = stats.timeoutCount.sum();
            
            if (typeMetrics.totalCount > 0) {
                typeMetrics.successRate = (double) typeMetrics.successCount / typeMetrics.totalCount * 100;
            }
            
            if (typeMetrics.successCount > 0) {
                typeMetrics.averageResponseTime = (double) stats.totalResponseTime.get() / typeMetrics.successCount;
            }
            
            typeMetrics.maxResponseTime = stats.maxResponseTime.get() == 0 ? 0 : stats.maxResponseTime.get();
            typeMetrics.minResponseTime = stats.minResponseTime.get() == Long.MAX_VALUE ? 0 : stats.minResponseTime.get();
            
            metrics.commandTypeMetrics.put(type, typeMetrics);
        });
        
        return metrics;
    }
    
    /**
     * 重置所有统计信息
     */
    public void resetMetrics() {
        totalCommands.reset();
        successfulCommands.reset();
        failedCommands.reset();
        timeoutCommands.reset();
        totalResponseTime.set(0);
        maxResponseTime.set(0);
        minResponseTime.set(Long.MAX_VALUE);
        
        commandTypeStats.clear();
        recentResponseTimes.clear();
        
        logger.info("Performance metrics reset");
    }
    
    /**
     * 更新最大最小响应时间
     */
    private void updateMinMaxResponseTime(long responseTime) {
        // 更新最大响应时间
        long currentMax = maxResponseTime.get();
        while (responseTime > currentMax && !maxResponseTime.compareAndSet(currentMax, responseTime)) {
            currentMax = maxResponseTime.get();
        }
        
        // 更新最小响应时间
        long currentMin = minResponseTime.get();
        while (responseTime < currentMin && !minResponseTime.compareAndSet(currentMin, responseTime)) {
            currentMin = minResponseTime.get();
        }
    }
    
    /**
     * 计算最近1分钟的平均响应时间
     */
    private double calculateRecentAverageResponseTime() {
        long oneMinuteAgo = System.currentTimeMillis() - 60000;
        
        long totalTime = 0;
        int count = 0;
        
        for (var entry : recentResponseTimes.entrySet()) {
            if (entry.getKey() > oneMinuteAgo) {
                totalTime += entry.getValue();
                count++;
            }
        }
        
        return count > 0 ? (double) totalTime / count : 0;
    }
    
    /**
     * 清理旧的响应时间记录
     */
    private void cleanupOldMetrics() {
        long fiveMinutesAgo = System.currentTimeMillis() - 300000; // 5分钟前
        recentResponseTimes.entrySet().removeIf(entry -> entry.getKey() < fiveMinutesAgo);
    }
    
    /**
     * 输出性能报告
     */
    private void reportMetrics() {
        try {
            PerformanceMetrics metrics = getMetrics();
            
            logger.info("=== Hardware Performance Report ===");
            logger.info("Total Commands: {}, Success: {}, Failed: {}, Timeout: {}", 
                       metrics.totalCommands, metrics.successfulCommands, 
                       metrics.failedCommands, metrics.timeoutCommands);
            logger.info("Success Rate: {:.2f}%, Failure Rate: {:.2f}%, Timeout Rate: {:.2f}%",
                       metrics.successRate, metrics.failureRate, metrics.timeoutRate);
            logger.info("Response Time - Avg: {:.2f}ms, Min: {}ms, Max: {}ms, Recent Avg: {:.2f}ms",
                       metrics.averageResponseTime, metrics.minResponseTime, 
                       metrics.maxResponseTime, metrics.recentAverageResponseTime);
            
            // 输出各命令类型的统计
            metrics.commandTypeMetrics.forEach((type, typeMetrics) -> {
                logger.info("Command [{}] - Total: {}, Success: {}, Success Rate: {:.2f}%, Avg Response: {:.2f}ms",
                           type, typeMetrics.totalCount, typeMetrics.successCount,
                           typeMetrics.successRate, typeMetrics.averageResponseTime);
            });
            
            logger.info("=== End Performance Report ===");
            
        } catch (Exception e) {
            logger.error("Error generating performance report", e);
        }
    }
    
    /**
     * 命令统计信息
     */
    private static class CommandStats {
        final LongAdder totalCount = new LongAdder();
        final LongAdder successCount = new LongAdder();
        final LongAdder failureCount = new LongAdder();
        final LongAdder timeoutCount = new LongAdder();
        final AtomicLong totalResponseTime = new AtomicLong(0);
        final AtomicLong maxResponseTime = new AtomicLong(0);
        final AtomicLong minResponseTime = new AtomicLong(Long.MAX_VALUE);
        
        void updateMinMaxResponseTime(long responseTime) {
            // 更新最大响应时间
            long currentMax = maxResponseTime.get();
            while (responseTime > currentMax && !maxResponseTime.compareAndSet(currentMax, responseTime)) {
                currentMax = maxResponseTime.get();
            }
            
            // 更新最小响应时间
            long currentMin = minResponseTime.get();
            while (responseTime < currentMin && !minResponseTime.compareAndSet(currentMin, responseTime)) {
                currentMin = minResponseTime.get();
            }
        }
    }
    
    /**
     * 性能指标数据类
     */
    public static class PerformanceMetrics {
        public long totalCommands;
        public long successfulCommands;
        public long failedCommands;
        public long timeoutCommands;
        public double successRate;
        public double failureRate;
        public double timeoutRate;
        public double averageResponseTime;
        public long maxResponseTime;
        public long minResponseTime;
        public double recentAverageResponseTime;
        public final ConcurrentHashMap<String, CommandTypeMetrics> commandTypeMetrics = new ConcurrentHashMap<>();
    }
    
    /**
     * 命令类型指标数据类
     */
    public static class CommandTypeMetrics {
        public String commandType;
        public long totalCount;
        public long successCount;
        public long failureCount;
        public long timeoutCount;
        public double successRate;
        public double averageResponseTime;
        public long maxResponseTime;
        public long minResponseTime;
    }
}