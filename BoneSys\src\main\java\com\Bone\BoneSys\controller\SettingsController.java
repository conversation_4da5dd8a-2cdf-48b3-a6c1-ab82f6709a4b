package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.service.SettingsService;
import com.Bone.BoneSys.service.SettingsService.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;



/**
 * 系统设置控制器
 * 提供系统参数配置、设备配置等管理接口
 */
@RestController
@RequestMapping("/api/settings")
@CrossOrigin(origins = "*")
public class SettingsController {

    private static final Logger logger = LoggerFactory.getLogger(SettingsController.class);

    @Autowired
    private SettingsService settingsService;

    /**
     * 获取系统参数配置
     * GET /api/settings/parameters
     */
    @GetMapping("/parameters")
    public ApiResponse<SystemParameters> getSystemParameters() {
        try {
            logger.info("API request: get system parameters");
            
            SystemParameters parameters = settingsService.getSystemParameters();
            
            logger.info("Successfully retrieved system parameters");
            return ApiResponse.success("系统参数获取成功", parameters);
            
        } catch (Exception e) {
            logger.error("Error retrieving system parameters", e);
            return ApiResponse.error(500, "获取系统参数失败: " + e.getMessage());
        }
    }

    /**
     * 更新系统参数配置
     * POST /api/settings/parameters
     */
    @PostMapping("/parameters")
    public ApiResponse<Void> updateSystemParameters(@RequestBody SystemParametersUpdateRequest request) {
        try {
            logger.info("API request: update system parameters: {}", request);
            
            settingsService.updateSystemParameters(request);
            
            logger.info("Successfully updated system parameters");
            return ApiResponse.success("系统参数更新成功", null);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid system parameters: {}", e.getMessage());
            return ApiResponse.error(400, "参数验证失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Error updating system parameters", e);
            return ApiResponse.error(500, "更新系统参数失败: " + e.getMessage());
        }
    }

    /**
     * 获取设备配置信息
     * GET /api/settings/device-config
     */
    @GetMapping("/device-config")
    public ApiResponse<DeviceConfigurationInfo> getDeviceConfiguration() {
        try {
            logger.info("API request: get device configuration");
            
            DeviceConfigurationInfo config = settingsService.getDeviceConfiguration();
            
            logger.info("Successfully retrieved device configuration");
            return ApiResponse.success("设备配置获取成功", config);
            
        } catch (Exception e) {
            logger.error("Error retrieving device configuration", e);
            return ApiResponse.error(500, "获取设备配置失败: " + e.getMessage());
        }
    }

    /**
     * 重置系统参数为默认值
     * POST /api/settings/reset-defaults
     */
    @PostMapping("/reset-defaults")
    public ApiResponse<Void> resetToDefaults() {
        try {
            logger.info("API request: reset system parameters to defaults");
            
            settingsService.resetToDefaults();
            
            logger.info("Successfully reset system parameters to defaults");
            return ApiResponse.success("系统参数已重置为默认值", null);
            
        } catch (Exception e) {
            logger.error("Error resetting system parameters", e);
            return ApiResponse.error(500, "重置系统参数失败: " + e.getMessage());
        }
    }

    /**
     * 获取治疗参数配置
     * GET /api/settings/treatment-parameters
     */
    @GetMapping("/treatment-parameters")
    public ApiResponse<TreatmentParameters> getTreatmentParameters() {
        try {
            logger.info("API request: get treatment parameters");
            
            SystemParameters systemParams = settingsService.getSystemParameters();
            TreatmentParameters treatmentParams = systemParams.getTreatmentParameters();
            
            logger.info("Successfully retrieved treatment parameters");
            return ApiResponse.success("治疗参数获取成功", treatmentParams);
            
        } catch (Exception e) {
            logger.error("Error retrieving treatment parameters", e);
            return ApiResponse.error(500, "获取治疗参数失败: " + e.getMessage());
        }
    }

    /**
     * 更新治疗参数配置
     * POST /api/settings/treatment-parameters
     */
    @PostMapping("/treatment-parameters")
    public ApiResponse<Void> updateTreatmentParameters(@RequestBody TreatmentParameters treatmentParameters) {
        try {
            logger.info("API request: update treatment parameters: {}", treatmentParameters);
            
            SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
            request.setTreatmentParameters(treatmentParameters);
            
            settingsService.updateSystemParameters(request);
            
            logger.info("Successfully updated treatment parameters");
            return ApiResponse.success("治疗参数更新成功", null);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid treatment parameters: {}", e.getMessage());
            return ApiResponse.error(400, "参数验证失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Error updating treatment parameters", e);
            return ApiResponse.error(500, "更新治疗参数失败: " + e.getMessage());
        }
    }

    /**
     * 获取串口配置
     * GET /api/settings/serial-config
     */
    @GetMapping("/serial-config")
    public ApiResponse<SerialConfiguration> getSerialConfiguration() {
        try {
            logger.info("API request: get serial configuration");
            
            SystemParameters systemParams = settingsService.getSystemParameters();
            SerialConfiguration serialConfig = systemParams.getSerialConfiguration();
            
            logger.info("Successfully retrieved serial configuration");
            return ApiResponse.success("串口配置获取成功", serialConfig);
            
        } catch (Exception e) {
            logger.error("Error retrieving serial configuration", e);
            return ApiResponse.error(500, "获取串口配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新串口配置
     * POST /api/settings/serial-config
     */
    @PostMapping("/serial-config")
    public ApiResponse<Void> updateSerialConfiguration(@RequestBody SerialConfiguration serialConfiguration) {
        try {
            logger.info("API request: update serial configuration: {}", serialConfiguration);
            
            SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
            request.setSerialConfiguration(serialConfiguration);
            
            settingsService.updateSystemParameters(request);
            
            logger.info("Successfully updated serial configuration");
            return ApiResponse.success("串口配置更新成功", null);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid serial configuration: {}", e.getMessage());
            return ApiResponse.error(400, "参数验证失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Error updating serial configuration", e);
            return ApiResponse.error(500, "更新串口配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取硬件配置
     * GET /api/settings/hardware-config
     */
    @GetMapping("/hardware-config")
    public ApiResponse<HardwareConfiguration> getHardwareConfiguration() {
        try {
            logger.info("API request: get hardware configuration");
            
            SystemParameters systemParams = settingsService.getSystemParameters();
            HardwareConfiguration hardwareConfig = systemParams.getHardwareConfiguration();
            
            logger.info("Successfully retrieved hardware configuration");
            return ApiResponse.success("硬件配置获取成功", hardwareConfig);
            
        } catch (Exception e) {
            logger.error("Error retrieving hardware configuration", e);
            return ApiResponse.error(500, "获取硬件配置失败: " + e.getMessage());
        }
    }

    /**
     * 更新硬件配置
     * POST /api/settings/hardware-config
     */
    @PostMapping("/hardware-config")
    public ApiResponse<Void> updateHardwareConfiguration(@RequestBody HardwareConfiguration hardwareConfiguration) {
        try {
            logger.info("API request: update hardware configuration: {}", hardwareConfiguration);
            
            SystemParametersUpdateRequest request = new SystemParametersUpdateRequest();
            request.setHardwareConfiguration(hardwareConfiguration);
            
            settingsService.updateSystemParameters(request);
            
            logger.info("Successfully updated hardware configuration");
            return ApiResponse.success("硬件配置更新成功", null);
            
        } catch (IllegalArgumentException e) {
            logger.warn("Invalid hardware configuration: {}", e.getMessage());
            return ApiResponse.error(400, "参数验证失败: " + e.getMessage());
        } catch (Exception e) {
            logger.error("Error updating hardware configuration", e);
            return ApiResponse.error(500, "更新硬件配置失败: " + e.getMessage());
        }
    }

    /**
     * 获取系统状态信息
     * GET /api/settings/system-status
     */
    @GetMapping("/system-status")
    public ApiResponse<SystemStatusInfo> getSystemStatus() {
        try {
            logger.info("API request: get system status");
            
            SystemStatusInfo status = new SystemStatusInfo();
            status.setSystemRunning(true);
            status.setConfigurationValid(true);
            status.setLastConfigUpdate(java.time.LocalDateTime.now().toString());
            status.setActiveProfile(settingsService.getSystemParameters().getEnvironment());
            
            logger.info("Successfully retrieved system status");
            return ApiResponse.success("系统状态获取成功", status);
            
        } catch (Exception e) {
            logger.error("Error retrieving system status", e);
            return ApiResponse.error(500, "获取系统状态失败: " + e.getMessage());
        }
    }

    // 内部DTO类
    public static class SystemStatusInfo {
        private Boolean systemRunning;
        private Boolean configurationValid;
        private String lastConfigUpdate;
        private String activeProfile;

        // Getters and Setters
        public Boolean getSystemRunning() { return systemRunning; }
        public void setSystemRunning(Boolean systemRunning) { this.systemRunning = systemRunning; }
        
        public Boolean getConfigurationValid() { return configurationValid; }
        public void setConfigurationValid(Boolean configurationValid) { this.configurationValid = configurationValid; }
        
        public String getLastConfigUpdate() { return lastConfigUpdate; }
        public void setLastConfigUpdate(String lastConfigUpdate) { this.lastConfigUpdate = lastConfigUpdate; }
        
        public String getActiveProfile() { return activeProfile; }
        public void setActiveProfile(String activeProfile) { this.activeProfile = activeProfile; }
    }
}
