package com.Bone.BoneSys.controller;

import com.Bone.BoneSys.dto.ApiResponse;
import com.Bone.BoneSys.entity.Patient;
import com.Bone.BoneSys.entity.Record;
import com.Bone.BoneSys.repository.PatientRepository;
import com.Bone.BoneSys.repository.RecordRepository;
import lombok.Data;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.web.bind.annotation.*;

import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 档案管理控制器
 * 对应UI页面：新版-档案管理.png
 */
@RestController
@RequestMapping("/api/records")
@CrossOrigin(origins = "*")
public class RecordController {

    private static final Logger logger = LoggerFactory.getLogger(RecordController.class);

    @Autowired
    private RecordRepository recordRepository;
    
    @Autowired
    private PatientRepository patientRepository;

    /**
     * 获取档案管理页面数据
     * GET /api/records
     */
    @GetMapping
    public ApiResponse<RecordListResponse> getRecords(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search,
            @RequestParam(required = false) String cardId) {
        
        try {
            logger.info("Fetching records list - page: {}, size: {}, search: {}, cardId: {}", 
                       page, size, search, cardId);

            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
            Page<Record> recordPage;

            // 根据搜索条件查询
            if (search != null && !search.trim().isEmpty()) {
                recordPage = recordRepository.findByPatientNameContainingOrPatientCardIdContaining(
                    search.trim(), search.trim(), pageable);
            } else if (cardId != null && !cardId.trim().isEmpty()) {
                recordPage = recordRepository.findByPatientCardId(cardId.trim(), pageable);
            } else {
                recordPage = recordRepository.findAll(pageable);
            }

            // 转换为响应格式
            List<RecordItem> records = recordPage.getContent().stream()
                .map(this::convertToRecordItem)
                .collect(Collectors.toList());

            RecordListResponse response = new RecordListResponse();
            response.setRecords(records);
            response.setPagination(new PaginationInfo(
                page,
                recordPage.getTotalPages(),
                (int) recordPage.getTotalElements(),
                size
            ));

            return ApiResponse.success("档案列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching records list", e);
            return ApiResponse.error(500, "获取档案列表失败: " + e.getMessage());
        }
    }

    /**
     * 删除档案
     * DELETE /api/records/{recordId}
     */
    @DeleteMapping("/{recordId}")
    public ApiResponse<Void> deleteRecord(
            @PathVariable Long recordId,
            @RequestBody DeleteRecordRequest request) {
        
        try {
            logger.info("Deleting record: {}", recordId);

            // 验证密码（这里简化处理，实际应该验证管理员密码）
            if (request.getPassword() == null || request.getPassword().trim().isEmpty()) {
                return ApiResponse.error(400, "密码不能为空");
            }

            // 检查档案是否存在
            if (!recordRepository.existsById(recordId)) {
                return ApiResponse.error(404, "档案不存在");
            }

            // 简单密码验证（实际应该加密验证）
            if (!"admin123".equals(request.getPassword())) {
                return ApiResponse.error(403, "密码错误");
            }

            // 删除档案
            recordRepository.deleteById(recordId);

            return ApiResponse.success("删除成功");

        } catch (Exception e) {
            logger.error("Error deleting record: {}", recordId, e);
            return ApiResponse.error(500, "删除档案失败: " + e.getMessage());
        }
    }

    /**
     * 获取新建档案候选列表
     * GET /api/records/candidates
     */
    @GetMapping("/candidates")
    public ApiResponse<CandidateListResponse> getCandidates(
            @RequestParam(defaultValue = "1") int page,
            @RequestParam(defaultValue = "10") int size,
            @RequestParam(required = false) String search) {
        
        try {
            logger.info("Fetching candidate list - page: {}, size: {}, search: {}", page, size, search);

            Pageable pageable = PageRequest.of(page - 1, size, Sort.by("createdAt").descending());
            Page<Patient> patientPage;

            // 根据搜索条件查询患者（这里简化为查询所有患者）
            if (search != null && !search.trim().isEmpty()) {
                patientPage = patientRepository.findByNameContainingOrPatientCardIdContaining(
                    search.trim(), search.trim(), pageable);
            } else {
                patientPage = patientRepository.findAll(pageable);
            }

            // 转换为候选项格式
            List<CandidateItem> candidates = patientPage.getContent().stream()
                .map(this::convertToCandidateItem)
                .collect(Collectors.toList());

            CandidateListResponse response = new CandidateListResponse();
            response.setCandidates(candidates);
            response.setPagination(new PaginationInfo(
                page,
                patientPage.getTotalPages(),
                (int) patientPage.getTotalElements(),
                size
            ));

            return ApiResponse.success("候选列表获取成功", response);

        } catch (Exception e) {
            logger.error("Error fetching candidates list", e);
            return ApiResponse.error(500, "获取候选列表失败: " + e.getMessage());
        }
    }

    /**
     * 转换Record为RecordItem
     */
    private RecordItem convertToRecordItem(Record record) {
        RecordItem item = new RecordItem();
        Patient patient = record.getPatient();
        
        item.setPatientId(patient.getId());
        item.setCardId(patient.getPatientCardId());
        item.setName(patient.getName());
        item.setAge(patient.getAge() != null ? patient.getAge() + "岁" : "未知");
        item.setGender(patient.getGender() != null ? patient.getGender() : "未知");
        item.setTotalTreatments(record.getSessionsCompletedCount());
        item.setCreatedDate(record.getCreatedAt().format(DateTimeFormatter.ofPattern("yyyy-MM-dd")));
        
        return item;
    }

    /**
     * 转换Patient为CandidateItem
     */
    private CandidateItem convertToCandidateItem(Patient patient) {
        CandidateItem item = new CandidateItem();
        
        item.setCardId(patient.getPatientCardId());
        item.setName(patient.getName());
        item.setAge(patient.getAge() != null ? patient.getAge() + "岁" : "未知");
        item.setGender(patient.getGender() != null ? patient.getGender() : "未知");
        item.setAppointmentTime("2025-07-27 14:30"); // 模拟预约时间
        item.setBodyPart("待确定"); // 模拟治疗部位
        item.setSessions(0); // 模拟次数
        
        return item;
    }

    // DTO类定义
    @Data
    public static class RecordListResponse {
        private List<RecordItem> records;
        private PaginationInfo pagination;
    }

    @Data
    public static class RecordItem {
        private Long patientId;
        private String cardId;
        private String name;
        private String age;
        private String gender;
        private Integer totalTreatments;
        private String createdDate;
    }

    @Data
    public static class CandidateListResponse {
        private List<CandidateItem> candidates;
        private PaginationInfo pagination;
    }

    @Data
    public static class CandidateItem {
        private String cardId;
        private String name;
        private String age;
        private String gender;
        private String appointmentTime;
        private String bodyPart;
        private Integer sessions;
    }

    @Data
    public static class PaginationInfo {
        private int currentPage;
        private int totalPages;
        private int totalRecords;
        private int pageSize;

        public PaginationInfo(int currentPage, int totalPages, int totalRecords, int pageSize) {
            this.currentPage = currentPage;
            this.totalPages = totalPages;
            this.totalRecords = totalRecords;
            this.pageSize = pageSize;
        }
    }

    @Data
    public static class DeleteRecordRequest {
        private String password;
    }
}
