@echo off
chcp 65001 >nul
echo ====================================
echo FREEBONE医疗系统 - 数据库检查工具
echo ====================================
echo.

echo 正在检查MySQL服务状态...
sc query mysql >nul 2>&1
if %errorlevel% == 0 (
    echo ✅ MySQL服务正在运行
) else (
    echo ❌ MySQL服务未运行，正在启动...
    net start mysql
    if %errorlevel% == 0 (
        echo ✅ MySQL服务启动成功
    ) else (
        echo ❌ MySQL服务启动失败，请手动启动
        pause
        exit /b 1
    )
)

echo.
echo 正在检查数据库连接...
mysql -u root -p -e "SELECT 'MySQL连接成功' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ 数据库连接正常
) else (
    echo ❌ 数据库连接失败，请检查密码
    pause
    exit /b 1
)

echo.
echo 正在检查bonesys数据库...
mysql -u root -p -e "USE bonesys; SELECT 'bonesys数据库存在' as status;" 2>nul
if %errorlevel% == 0 (
    echo ✅ bonesys数据库存在
    
    echo.
    echo 正在检查用户表...
    mysql -u root -p -e "USE bonesys; SELECT id, username, '厂家密码已设置' as factory_pwd, CASE WHEN user_password_hash = factory_password_hash THEN '首次登录状态' ELSE '用户密码已设置' END as user_pwd_status FROM users WHERE id = 1;" 2>nul
    if %errorlevel% == 0 (
        echo ✅ 用户数据检查完成
    ) else (
        echo ❌ 用户表检查失败
    )
) else (
    echo ❌ bonesys数据库不存在，正在创建...
    mysql -u root -p < SQL/persistent_database_setup.sql
    if %errorlevel% == 0 (
        echo ✅ 数据库创建成功
    ) else (
        echo ❌ 数据库创建失败
        pause
        exit /b 1
    )
)

echo.
echo 正在检查治疗头数据...
mysql -u root -p -e "USE bonesys; SELECT COUNT(*) as '治疗头数量' FROM treatment_heads;" 2>nul

echo.
echo ====================================
echo 数据库检查完成
echo ====================================
echo.
echo 登录信息：
echo - 首次登录密码：factory123
echo - 登录后请设置用户密码
echo.
pause