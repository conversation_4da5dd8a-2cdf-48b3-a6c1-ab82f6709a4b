import axios from 'axios';
import { MessagePlugin } from 'tdesign-vue-next';

// 创建axios实例
const axiosInstance = axios.create({
  // 使用相对路径，让Vite代理处理请求
  baseURL: '/api',
  timeout: 10000, // 请求超时时间
  headers: {
    'Content-Type': 'application/json'
  }
});

// 请求拦截器
axiosInstance.interceptors.request.use(
  config => {
    // 在发送请求前可以做一些处理，例如添加token
    // const token = localStorage.getItem('token');
    // if (token) {
    //   config.headers.Authorization = `Bearer ${token}`;
    // }
    return config;
  },
  error => {
    console.error('请求错误:', error);
    return Promise.reject(error);
  }
);

// 响应拦截器
axiosInstance.interceptors.response.use(
  response => {
    // 对响应数据做处理
    return response;
  },
  error => {
    if (error.response) {
      switch (error.response.status) {
        case 401:
          // 未授权，跳转到登录页
          MessagePlugin.error('登录已过期，请重新登录');
          localStorage.removeItem('isAuthenticated');
          window.location.href = '/login';
          break;
        case 403:
          MessagePlugin.error('没有权限执行此操作');
          break;
        case 404:
          MessagePlugin.error('请求的资源不存在');
          break;
        case 500:
          MessagePlugin.error('服务器错误，请稍后重试');
          break;
        default:
          MessagePlugin.error(`请求失败: ${error.response.data.message || '未知错误'}`);
      }
    } else if (error.request) {
      // 请求已发出，但没有收到响应
      MessagePlugin.error('网络错误，无法连接到服务器');
    } else {
      // 请求配置出错
      MessagePlugin.error('请求配置错误');
    }
    return Promise.reject(error);
  }
);

export default axiosInstance; 