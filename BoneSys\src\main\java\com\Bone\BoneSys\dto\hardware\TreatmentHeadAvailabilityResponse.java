package com.Bone.BoneSys.dto.hardware;

import java.util.List;
import java.util.Map;
import java.util.HashMap;

/**
 * 治疗头可用性检查响应
 */
public class TreatmentHeadAvailabilityResponse {
    
    private boolean sufficient; // 治疗头数量是否充足
    private int availableCount; // 可用治疗头数量
    private int requiredCount; // 需要的治疗头数量
    private List<TreatmentHeadInfo> availableHeads; // 可用的治疗头列表
    private List<TreatmentHeadRecommendation> recommendations; // 推荐的治疗头列表
    private String message; // 提示信息
    private AvailabilityDetail availabilityDetail; // 详细可用性信息
    
    public TreatmentHeadAvailabilityResponse() {}
    
    public TreatmentHeadAvailabilityResponse(boolean sufficient, int availableCount, int requiredCount, 
                                           List<TreatmentHeadInfo> availableHeads, 
                                           List<TreatmentHeadRecommendation> recommendations, 
                                           String message) {
        this.sufficient = sufficient;
        this.availableCount = availableCount;
        this.requiredCount = requiredCount;
        this.availableHeads = availableHeads;
        this.recommendations = recommendations;
        this.message = message;
    }
    
    public TreatmentHeadAvailabilityResponse(boolean sufficient, int availableCount, int requiredCount, 
                                           List<TreatmentHeadInfo> availableHeads, 
                                           List<TreatmentHeadRecommendation> recommendations, 
                                           String message, AvailabilityDetail availabilityDetail) {
        this.sufficient = sufficient;
        this.availableCount = availableCount;
        this.requiredCount = requiredCount;
        this.availableHeads = availableHeads;
        this.recommendations = recommendations;
        this.message = message;
        this.availabilityDetail = availabilityDetail;
    }
    
    // Getters and Setters
    public boolean isSufficient() {
        return sufficient;
    }
    
    public void setSufficient(boolean sufficient) {
        this.sufficient = sufficient;
    }
    
    public int getAvailableCount() {
        return availableCount;
    }
    
    public void setAvailableCount(int availableCount) {
        this.availableCount = availableCount;
    }
    
    public int getRequiredCount() {
        return requiredCount;
    }
    
    public void setRequiredCount(int requiredCount) {
        this.requiredCount = requiredCount;
    }
    
    public List<TreatmentHeadInfo> getAvailableHeads() {
        return availableHeads;
    }
    
    public void setAvailableHeads(List<TreatmentHeadInfo> availableHeads) {
        this.availableHeads = availableHeads;
    }
    
    public List<TreatmentHeadRecommendation> getRecommendations() {
        return recommendations;
    }
    
    public void setRecommendations(List<TreatmentHeadRecommendation> recommendations) {
        this.recommendations = recommendations;
    }
    
    public String getMessage() {
        return message;
    }
    
    public void setMessage(String message) {
        this.message = message;
    }
    
    public AvailabilityDetail getAvailabilityDetail() {
        return availabilityDetail;
    }
    
    public void setAvailabilityDetail(AvailabilityDetail availabilityDetail) {
        this.availabilityDetail = availabilityDetail;
    }
    
    /**
     * 获取身体部位颜色映射
     * 返回每个身体部位对应的指示灯颜色
     */
    public Map<String, String> getBodyPartColorMapping() {
        Map<String, String> colorMapping = new HashMap<>();
        
        if (recommendations != null) {
            for (TreatmentHeadRecommendation rec : recommendations) {
                String bodyPart = rec.getTargetBodyPart();
                String colorName = rec.getLightColorName();
                
                if (bodyPart != null && colorName != null && !colorMapping.containsKey(bodyPart)) {
                    colorMapping.put(bodyPart, colorName);
                }
            }
        }
        
        return colorMapping;
    }
    
    /**
     * 获取按身体部位分组的推荐列表
     */
    public Map<String, List<TreatmentHeadRecommendation>> getRecommendationsByBodyPart() {
        if (recommendations == null) {
            return new HashMap<>();
        }
        
        return recommendations.stream()
            .filter(rec -> rec.getTargetBodyPart() != null)
            .collect(java.util.stream.Collectors.groupingBy(
                TreatmentHeadRecommendation::getTargetBodyPart
            ));
    }
    
    @Override
    public String toString() {
        return String.format("TreatmentHeadAvailabilityResponse{sufficient=%s, availableCount=%d, " +
                           "requiredCount=%d, availableHeads=%d items, recommendations=%d items, " +
                           "message='%s', availabilityDetail=%s}", 
                           sufficient, availableCount, requiredCount, 
                           availableHeads != null ? availableHeads.size() : 0,
                           recommendations != null ? recommendations.size() : 0,
                           message, availabilityDetail);
    }
}