import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },
  publicDir: 'public',
  assetsInclude: ['**/*.png', '**/*.jpg', '**/*.jpeg', '**/*.gif', '**/*.svg'],
  server: {
    host: '0.0.0.0',

    proxy: {
      // 将所有/api开头的请求代理到后端服务器
      '/api': {
        target: 'http://127.0.0.1:8080',
        changeOrigin: true,
        secure: false
        // 后端API包含/api前缀，所以不需要rewrite
      }
    }
  },
  define: {
    __LANHU_BASE__: '"/lanhu_html"'
  }
})
