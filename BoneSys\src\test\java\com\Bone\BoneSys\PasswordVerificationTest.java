package com.Bone.BoneSys;

import org.junit.jupiter.api.Test;
import org.springframework.security.crypto.bcrypt.BCrypt;

import static org.junit.jupiter.api.Assertions.*;

/**
 * 密码验证测试
 * 验证数据库中的BCrypt哈希值对应的原始密码
 */
public class PasswordVerificationTest {

    @Test
    void testPasswordVerification() {
        // 数据库中的BCrypt哈希值
        String storedHash = "$2a$10$N.zmdr9k7uOCQb376NoUnuTJ8iAt6Z5EHsM5lE9cY8jLbveVFWQou";

        // 测试可能的密码
        String[] possiblePasswords = {
                "admin123",
                "hello.world333",
                "admin",
                "123456",
                "password",
                "freebone",
                "FREEBONE",
                "Mybone",
                "bonesys",
                "BONESYS",
                "factory123"
        };

        System.out.println("🔐 验证数据库中的BCrypt哈希值对应的原始密码");
        System.out.println("哈希值: " + storedHash);
        System.out.println();

        for (String password : possiblePasswords) {
            boolean matches = BCrypt.checkpw(password, storedHash);
            System.out.println("密码 '" + password + "': " + (matches ? "✅ 匹配" : "❌ 不匹配"));

            if (matches) {
                System.out.println("🎉 找到正确密码: " + password);
                assertTrue(matches, "密码应该匹配");
                return;
            }
        }

        fail("未找到匹配的密码");
    }

    @Test
    void testGenerateNewBCryptHash() {
        System.out.println("🔧 生成新的BCrypt哈希值示例");

        String[] passwords = { "admin123", "newpassword", "test123" };

        for (String password : passwords) {
            String hash = BCrypt.hashpw(password, BCrypt.gensalt());
            boolean verify = BCrypt.checkpw(password, hash);

            System.out.println("原始密码: " + password);
            System.out.println("BCrypt哈希: " + hash);
            System.out.println("验证结果: " + (verify ? "✅ 通过" : "❌ 失败"));
            System.out.println("---");
        }
    }
}
